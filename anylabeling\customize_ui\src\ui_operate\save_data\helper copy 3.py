from global_tools.postgre_sql import PostgreSQLClient
import traceback
from global_tools.ui_tools import LineEditManager, QLabelManager, LogOutput
import os
from global_tools.utils import Colors

# 导入表结构定义
from .scheam import DETECTION_RESULTS_TABLE
from global_tools.utils import Logger

import json
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional, Union
import threading  # 用于文件夹计数锁


# 删除多线程相关导入：ThreadPoolExecutor, as_completed


class DataProcessingManager:
	"""
	数据处理管理类，负责从UI获取参数、处理JSON数据、存储到数据库和保存图像

	属性:
		__line_edit_manager: 用于操作QLineEdit控件
		__checkbox_manager: 用于操作QCheckBox控件
		__label_manager: 用于操作QLabel控件
		__log_output: 用于在UI界面输出日志
		__logger: 用于在控制台输出日志
		__db_client: PostgreSQL数据库客户端
		__image_counter: 图像计数器，用于子文件夹分配
		__counter_lock: 线程锁，保证计数器线程安全

	使用示例:
		# 创建实例
		manager = DataProcessingManager(
			line_edit_manager,
			checkbox_manager,
			label_manager,
			log_output,
			logger
		)

		# 处理数据
		result = manager.process_data()

		# 输出处理结果
		print(f"处理结果: {result}")
	"""

	def __init__(
			self, line_edit_manager: LineEditManager, checkbox_manager, label_manager, log_output, logger: Logger
	):
		"""
		初始化数据处理管理器

		参数:
			line_edit_manager: 操作QLineEdit的管理器实例
			checkbox_manager: 操作QCheckBox的管理器实例
			label_manager: 操作QLabel的管理器实例
			log_output: UI界面日志输出工具
			logger: 控制台日志输出工具
		"""
		self.__line_edit_manager = line_edit_manager
		self.__checkbox_manager = checkbox_manager
		self.__label_manager = label_manager
		self.__log_output = log_output
		self.__logger = logger
		self.__db_client = None  # 数据库连接
		self.__current_folder_index = None  # 当前使用的文件夹索引
		self.__current_folder_count = 0  # 当前文件夹已保存图像计数
		self.__counter_lock = threading.Lock()  # 恢复文件夹计数锁，确保文件夹管理的线程安全
		self.__folder_cache = { }  # 缓存子文件夹的索引与计数情况

		# 进度跟踪变量
		self.__current_progress = 0  # 当前进度
		self.__total_progress = 0  # 总进度
		self.__progress_label_name = "label_276"  # 进度显示标签名称

		# ==================== 综合相似度判断系统配置 ====================
		# 四维度相似度权重配置（用于计算综合相似度）
		self.__coordinate_weight = 0.4      # 坐标位置相似度权重40%
		self.__geometric_weight = 0.35      # 几何形状相似度权重35%
		self.__shape_feature_weight = 0.15  # 形状特征相似度权重15%
		self.__point_sequence_weight = 0.1  # 点序列相似度权重10%

		# 分层决策阈值配置（五层决策机制）
		# 第一层：极高相似度检查阈值（Early Exit）
		self.__coordinate_extreme_threshold = 0.95  # 坐标极高相似度阈值95%
		self.__geometric_extreme_threshold = 0.90   # 几何极高相似度阈值90%

		# 第二层：综合相似度评估阈值
		self.__comprehensive_high_threshold = 0.70  # 综合相似度过高阈值70%

		# 第三层：双重验证阈值
		self.__coordinate_double_threshold = 0.60   # 坐标双重验证阈值60%
		self.__geometric_double_threshold = 0.60    # 几何双重验证阈值60%

		# 第四层：单一维度高相似度阈值
		self.__coordinate_single_threshold = 0.70   # 坐标单一维度高阈值70%
		self.__geometric_single_threshold = 0.70    # 几何单一维度高阈值70%

		# 第五层：追加判断阈值（30%严格标准）
		self.__comprehensive_add_threshold = 0.30   # 综合相似度追加阈值30%

	def set_similarity_thresholds(
			self,
			coordinate_threshold: float = 0.8,
			shape_threshold: float = 0.8,
			comprehensive_threshold: float = 0.75,
			coordinate_weight: float = 0.6,
			shape_weight: float = 0.4
	):
		"""
		设置坐标和形状相似度判断阈值

		参数:
			coordinate_threshold: 坐标相似度阈值，默认0.8
			shape_threshold: 形状相似度阈值，默认0.8
			comprehensive_threshold: 综合相似度阈值，默认0.75
			coordinate_weight: 坐标相似度权重，默认0.6
			shape_weight: 形状相似度权重，默认0.4

		逻辑:
			坐标相似度 ≥ coordinate_threshold 且 形状相似度 ≥ shape_threshold → 跳过
			综合相似度 ≥ comprehensive_threshold → 跳过
			否则 → 追加

		使用示例:
			# 设置更严格的阈值
			manager.set_similarity_thresholds(
				coordinate_threshold=0.85,    # 85%以上坐标相似度跳过
				shape_threshold=0.85,         # 85%以上形状相似度跳过
				comprehensive_threshold=0.8,  # 80%以上综合相似度跳过
				coordinate_weight=0.7,        # 坐标权重70%
				shape_weight=0.3              # 形状权重30%
			)
		"""
		self.__coordinate_similarity_weight = coordinate_weight
		self.__shape_similarity_weight = shape_weight

		self.__log_output.append(
			f"相似度阈值已更新: 坐标≥{coordinate_threshold * 100:.0f}%, 形状≥{shape_threshold * 100:.0f}%, "
			f"综合≥{comprehensive_threshold * 100:.0f}%时跳过", Colors.GREEN
		)
		self.__logger.info(
			f"相似度阈值配置: coordinate={coordinate_threshold}, shape={shape_threshold}, "
			f"comprehensive={comprehensive_threshold}, weights=({coordinate_weight}, {shape_weight})"
		)

	def set_comprehensive_similarity_config(
			self,
			# 四维度权重配置
			coordinate_weight: float = 0.4,
			geometric_weight: float = 0.35,
			shape_feature_weight: float = 0.15,
			point_sequence_weight: float = 0.1,
			# 分层决策阈值配置
			coordinate_extreme_threshold: float = 0.95,
			geometric_extreme_threshold: float = 0.90,
			comprehensive_high_threshold: float = 0.70,
			coordinate_double_threshold: float = 0.60,
			geometric_double_threshold: float = 0.60,
			coordinate_single_threshold: float = 0.70,
			geometric_single_threshold: float = 0.70,
			comprehensive_add_threshold: float = 0.30
	):
		"""
		设置综合相似度判断系统的完整配置

		四维度相似度权重配置（用于计算综合相似度）:
			coordinate_weight: 坐标位置相似度权重，默认0.4（40%）
			geometric_weight: 几何形状相似度权重，默认0.35（35%）
			shape_feature_weight: 形状特征相似度权重，默认0.15（15%）
			point_sequence_weight: 点序列相似度权重，默认0.1（10%）

		五层决策机制阈值配置:
			第一层 - 极高相似度检查（Early Exit）:
				coordinate_extreme_threshold: 坐标极高相似度阈值，默认0.95（95%）
				geometric_extreme_threshold: 几何极高相似度阈值，默认0.90（90%）

			第二层 - 综合相似度评估:
				comprehensive_high_threshold: 综合相似度过高阈值，默认0.70（70%）

			第三层 - 双重验证:
				coordinate_double_threshold: 坐标双重验证阈值，默认0.60（60%）
				geometric_double_threshold: 几何双重验证阈值，默认0.60（60%）

			第四层 - 单一维度高相似度:
				coordinate_single_threshold: 坐标单一维度高阈值，默认0.70（70%）
				geometric_single_threshold: 几何单一维度高阈值，默认0.70（70%）

			第五层 - 追加判断（30%严格标准）:
				comprehensive_add_threshold: 综合相似度追加阈值，默认0.30（30%）

		决策逻辑:
			1. 坐标≥95% 或 几何≥90% → 跳过（极高相似度）
			2. 综合≥70% → 跳过（综合相似度过高）
			3. 坐标≥60% 且 几何≥60% → 跳过（双重验证）
			4. 坐标≥70% 或 几何≥70% → 跳过（单一维度高相似度）
			5. 综合≤30% → 追加（严格阈值标准）
			6. 其他情况 → 跳过（相似度超过30%阈值）

		使用示例:
			# 设置更严格的配置（更容易跳过重复数据）
			manager.set_comprehensive_similarity_config(
				coordinate_weight=0.5,              # 提高坐标权重到50%
				geometric_weight=0.3,               # 降低几何权重到30%
				coordinate_extreme_threshold=0.90,  # 降低坐标极高阈值到90%
				comprehensive_high_threshold=0.60,  # 降低综合高阈值到60%
				comprehensive_add_threshold=0.20    # 降低追加阈值到20%
			)

			# 设置更宽松的配置（更容易追加新数据）
			manager.set_comprehensive_similarity_config(
				coordinate_extreme_threshold=0.98,  # 提高坐标极高阈值到98%
				geometric_extreme_threshold=0.95,   # 提高几何极高阈值到95%
				comprehensive_high_threshold=0.80,  # 提高综合高阈值到80%
				comprehensive_add_threshold=0.40    # 提高追加阈值到40%
			)
		"""
		# 更新四维度权重配置
		self.__coordinate_weight = coordinate_weight
		self.__geometric_weight = geometric_weight
		self.__shape_feature_weight = shape_feature_weight
		self.__point_sequence_weight = point_sequence_weight

		# 更新分层决策阈值配置
		self.__coordinate_extreme_threshold = coordinate_extreme_threshold
		self.__geometric_extreme_threshold = geometric_extreme_threshold
		self.__comprehensive_high_threshold = comprehensive_high_threshold
		self.__coordinate_double_threshold = coordinate_double_threshold
		self.__geometric_double_threshold = geometric_double_threshold
		self.__coordinate_single_threshold = coordinate_single_threshold
		self.__geometric_single_threshold = geometric_single_threshold
		self.__comprehensive_add_threshold = comprehensive_add_threshold

		# 验证权重总和
		total_weight = coordinate_weight + geometric_weight + shape_feature_weight + point_sequence_weight
		if abs(total_weight - 1.0) > 0.01:  # 允许1%的误差
			self.__log_output.append(
				f"警告: 四维度权重总和为{total_weight:.3f}，建议调整为1.0", Colors.ORANGE
			)
			self.__logger.warning(f"四维度权重总和为{total_weight:.3f}，建议调整为1.0")

		# UI显示简洁配置信息
		self.__log_output.append(
			f"综合相似度配置已更新: 权重({coordinate_weight:.2f},{geometric_weight:.2f},"
			f"{shape_feature_weight:.2f},{point_sequence_weight:.2f}), 追加阈值≤{comprehensive_add_threshold*100:.0f}%",
			Colors.GREEN
		)

		# 控制台记录详细配置信息
		self.__logger.info("综合相似度判断系统配置已更新:")
		self.__logger.info(f"  四维度权重: 坐标={coordinate_weight}, 几何={geometric_weight}, "
						  f"特征={shape_feature_weight}, 序列={point_sequence_weight}")
		self.__logger.info(f"  极高阈值: 坐标≥{coordinate_extreme_threshold*100:.0f}%, "
						  f"几何≥{geometric_extreme_threshold*100:.0f}%")
		self.__logger.info(f"  综合高阈值: ≥{comprehensive_high_threshold*100:.0f}%")
		self.__logger.info(f"  双重验证阈值: 坐标≥{coordinate_double_threshold*100:.0f}%, "
						  f"几何≥{geometric_double_threshold*100:.0f}%")
		self.__logger.info(f"  单一维度高阈值: 坐标≥{coordinate_single_threshold*100:.0f}%, "
						  f"几何≥{geometric_single_threshold*100:.0f}%")
		self.__logger.info(f"  追加阈值: 综合≤{comprehensive_add_threshold*100:.0f}%")

	def __format_progress_text( self, current: int, total: int ) -> str:
		"""
		格式化进度显示文本

		参数:
			current: 当前进度值
			total: 总进度值

		返回:
			格式化的进度文本，格式为 "当前/总数 百分比%"

		使用示例:
			progress_text = self.__format_progress_text(5, 100)
			# 返回: "5/100 5.0%"
		"""
		if total <= 0:
			return "0/0 0.0%"

		percentage = (current / total) * 100
		return f"{current}/{total} {percentage:.1f}%"

	def __update_progress_display( self, current: int, total: int, label_name: str = None ) -> None:
		"""
		更新进度显示到UI标签

		参数:
			current: 当前进度值
			total: 总进度值
			label_name: 标签名称，默认使用self.__progress_label_name

		使用示例:
			# 更新进度到默认标签
			self.__update_progress_display(5, 100)

			# 更新进度到指定标签
			self.__update_progress_display(5, 100, "custom_label")
		"""
		try:
			if label_name is None:
				label_name = self.__progress_label_name

			if self.__label_manager and self.__label_manager.has_label( label_name ):
				progress_text = self.__format_progress_text( current, total )
				self.__label_manager.set_text( label_name, progress_text )

				# 添加淡入淡出动画效果，提升用户体验
				self.__label_manager.start_fade_animation( label_name, duration_ms=200 )

				self.__logger.debug( f"进度更新: {progress_text}" )
			else:
				self.__logger.warning( f"标签 {label_name} 不存在或不可用" )
		except Exception as e:
			self.__logger.error( f"更新进度显示时出错: {str( e )}" )

	# 不抛出异常，确保主流程继续

	def __initialize_progress( self, total: int ) -> None:
		"""
		初始化进度跟踪

		参数:
			total: 总进度值

		使用示例:
			self.__initialize_progress(100)  # 设置总进度为100
		"""
		self.__current_progress = 0
		self.__total_progress = total
		self.__update_progress_display( 0, total )
		self.__logger.info( f"进度初始化: 总计 {total} 项" )

	def __increment_progress( self, increment: int = 1 ) -> None:
		"""
		增加当前进度并更新显示

		参数:
			increment: 进度增量，默认为1

		使用示例:
			self.__increment_progress()     # 进度+1
			self.__increment_progress(5)    # 进度+5
		"""
		self.__current_progress += increment
		# 确保进度不超过总进度
		if self.__current_progress > self.__total_progress:
			self.__current_progress = self.__total_progress

		self.__update_progress_display( self.__current_progress, self.__total_progress )

	def __complete_progress( self ) -> None:
		"""
		完成进度显示（设置为100%）

		使用示例:
			self.__complete_progress()  # 显示完成状态
		"""
		self.__current_progress = self.__total_progress
		self.__update_progress_display( self.__current_progress, self.__total_progress )
		self.__logger.info( f"进度完成: {self.__current_progress}/{self.__total_progress}" )

	def get_current_progress( self ) -> tuple[ int, int ]:
		"""
		获取当前进度信息

		返回:
			(当前进度, 总进度) 的元组

		使用示例:
			current, total = self.get_current_progress()
			print(f"当前进度: {current}/{total}")
		"""
		return self.__current_progress, self.__total_progress

	def __get_ui_parameters( self ) -> Dict[ str, Any ]:
		"""
		从UI控件获取所有必要的参数

		返回:
			包含所有UI参数的字典，具体字段如下：
			- is_segment: 是否为分割模型 (布尔值)
			- is_obb: 是否为OBB（有向边界框）模型 (布尔值)
			- db_name: 数据库名称 (字符串)
			- table_name: 表名 (字符串)
			- data_source: 数据源标识 (字符串)
			- train_ratio: 训练集比例，取值0-1之间 (浮点数)
			- expected_unique_labels: 期望的唯一标签数量 (整数)
			- pretrain_ratio: 预训练数据比例，取值0-1之间 (浮点数)
			- input_folder: 输入文件夹路径 (字符串)
			- output_folder: 输出文件夹路径 (字符串)
			- clean_multilabel: 是否清理多标签数据 (布尔值)
		"""
		# 获取模型推理类型
		is_segment = self.__checkbox_manager.get_checked_state_by_object_name( "rotate_36" )  # 分割模型选择框
		is_obb = self.__checkbox_manager.get_checked_state_by_object_name( "rotate_37" )  # OBB模型选择框

		# 获取数据库参数
		db_name = self.__line_edit_manager.get_text( "lineEdit_68" ) or "postgres"  # 数据库名称，默认为postgres
		table_name = self.__line_edit_manager.get_text(
			"lineEdit_69"
		) or "detection_results"  # 表名，默认为detection_results
		data_source = self.__line_edit_manager.get_text( "lineEdit_62" ) or "default"  # 数据源标识，默认为default

		# 获取数据划分参数，格式为"训练比例:验证比例"，如"80:20"
		split_ratio_text = self.__line_edit_manager.get_text( "lineEdit_73" ) or "80:20"
		try:
			train_ratio, val_ratio = map( int, split_ratio_text.split( ':' ) )
			train_ratio = train_ratio / (train_ratio + val_ratio)  # 计算训练集占比
		except (ValueError, ZeroDivisionError):
			self.__log_output.append( f"无效的划分比例: {split_ratio_text}，使用默认值 0.8", Colors.ORANGE )
			self.__logger.warning( f"无效的划分比例: {split_ratio_text}，使用默认值 0.8" )
			train_ratio = 0.8  # 默认训练集占比为80%

		# 获取预期标签数量和预训练数据比例
		expected_unique_labels = self.__line_edit_manager.get_text( "lineEdit_76" ) or "1"  # 期望的唯一标签数量
		try:
			expected_unique_labels = int( expected_unique_labels )
		except ValueError:
			expected_unique_labels = 1  # 默认期望唯一标签数量为1

		pretrain_ratio_text = self.__line_edit_manager.get_text( "lineEdit_77" ) or "0.3"  # 预训练数据比例
		try:
			pretrain_ratio = float( pretrain_ratio_text )
		except ValueError:
			self.__log_output.append( f"无效的预训练比例: {pretrain_ratio_text}，使用默认值 0.3", Colors.ORANGE )
			self.__logger.warning( f"无效的预训练比例: {pretrain_ratio_text}，使用默认值 0.3" )
			pretrain_ratio = 0.3  # 默认预训练数据比例为30%

		# 获取输入和输出路径
		input_folder = self.__line_edit_manager.get_text( "lineEdit_48" ) or ""  # JSON文件输入文件夹路径
		output_folder = self.__line_edit_manager.get_text( "lineEdit_80" ) or ""  # 图像输出文件夹路径

		# 获取是否清理多标签数据
		clean_multilabel = self.__checkbox_manager.get_checked_state_by_object_name( "rotate_31" )  # 是否过滤掉含有多个标签的数据

		# 返回包含所有参数的字典
		return {
			"is_segment": is_segment,  # 是否为分割模型
			"is_obb": is_obb,  # 是否为OBB模型
			"db_name": db_name,  # 数据库名称
			"table_name": table_name,  # 表名
			"data_source": data_source,  # 数据源标识
			"train_ratio": train_ratio,  # 训练集比例
			"expected_unique_labels": expected_unique_labels,  # 期望的唯一标签数量
			"pretrain_ratio": pretrain_ratio,  # 预训练数据比例
			"input_folder": input_folder,  # 输入文件夹路径
			"output_folder": output_folder,  # 输出文件夹路径
			"clean_multilabel": clean_multilabel  # 是否清理多标签数据
		}

	def __connect_database( self, db_name: str ) -> bool:
		"""
		连接或创建PostgreSQL数据库，并保存配置用于线程本地连接

		参数:
			db_name: 数据库名称

		返回:
			连接成功返回True，否则返回False
		"""
		try:
			# 创建数据库连接（单线程版本）
			self.__db_client = PostgreSQLClient(
				host="localhost",
				port=5432,
				database=db_name,
				user="postgres",
				password="123456",
				min_connections=1,
				max_connections=5,
				application_name="X-AnyLabeling"
			)
			self.__log_output.append( f"成功连接到数据库: {db_name}", Colors.GREEN )
			self.__logger.info( f"成功连接到数据库: {db_name}" )
			return True
		except Exception as e:
			self.__log_output.append( f"数据库连接失败: {str( e )}", Colors.RED )
			self.__logger.error( f"数据库连接失败: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return False

	# 删除多线程相关方法：
	# - __get_thread_db_client()
	# - __cleanup_thread_connections()
	# - __log_connection_pool_status()

	def __table_exists( self, table_name: str ) -> bool:
		"""
		检查表是否存在

		参数:
			table_name: 表名

		返回:
			表存在返回True，否则返回False

		使用示例:
			exists = self.__table_exists("my_table")
			if exists:
				print("表已存在")
		"""
		if not self.__db_client:
			self.__log_output.append( "数据库未连接", Colors.RED )
			self.__logger.error( "数据库未连接" )
			return False

		try:
			# 使用PostgreSQL系统表查询表是否存在
			sql = """
				SELECT EXISTS (
					SELECT 1
					FROM information_schema.tables
					WHERE table_name = %s
					AND table_schema = 'public'
				)
			"""
			result = self.__db_client.execute_query( sql, (table_name,), fetch=True )

			if result and len( result ) > 0:
				exists = result[ 0 ][ 0 ]  # 获取EXISTS查询的结果
				# 只在控制台记录技术检查结果
				self.__logger.info( f"表 {table_name} 存在性检查结果: {exists}" )
				return exists
			else:
				self.__log_output.append( f"检查表 {table_name} 是否存在时查询无结果", Colors.RED )
				self.__logger.error( f"检查表 {table_name} 是否存在时查询无结果" )
				return False

		except Exception as e:
			self.__log_output.append( f"检查表 {table_name} 是否存在时出错: {str( e )}", Colors.RED )
			self.__logger.error( f"检查表 {table_name} 是否存在时出错: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return False

	def __create_table( self, table_name: str ) -> bool:
		"""
		创建数据表，如果表不存在

		参数:
			table_name: 表名

		返回:
			创建成功返回True，否则返回False
		"""
		if not self.__db_client:
			self.__log_output.append( "数据库未连接", Colors.RED )
			self.__logger.error( "数据库未连接" )
			return False

		try:
			# 先检查表是否存在
			if self.__table_exists( table_name ):
				# UI显示简洁信息
				self.__log_output.append( f"数据表已存在", Colors.GREEN )
				self.__logger.info( f"表 {table_name} 已存在，跳过创建" )
				return True

			# 使用预定义的Schema创建表
			schema_dict = DETECTION_RESULTS_TABLE.copy()
			# schema_dict["table_name"] = table_name  # 使用用户指定的表名
			del schema_dict[ 'table_name' ]
			# 创建表
			result = self.__db_client.create_table( table_name, schema_dict )

			if result.get( "success", False ):
				# UI显示简洁信息
				self.__log_output.append( f"数据表创建成功", Colors.GREEN )
				self.__logger.info( f"成功创建表: {table_name}" )
				return True
			else:
				self.__log_output.append( f"创建表失败: {result.get( 'error', '未知错误' )}", Colors.RED )
				self.__logger.error( f"创建表失败: {result.get( 'error', '未知错误' )}" )
				return False

		except Exception as e:
			self.__log_output.append( f"创建表失败: {str( e )}", Colors.RED )
			self.__logger.error( f"创建表失败: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return False

	def __read_json_files( self, folder_path: str ) -> List[ Dict[ str, Any ] ]:
		"""
		从指定文件夹读取所有JSON文件

		参数:
			folder_path: 包含JSON文件的文件夹路径

		返回:
			包含所有JSON数据的列表，每个元素是一个字典{数据:字典, 文件路径:字符串}
		"""
		json_data_list = [ ]
		folder_path = os.path.abspath( folder_path )

		if not os.path.exists( folder_path ):
			self.__log_output.append( f"文件夹不存在: {folder_path}", Colors.RED )
			self.__logger.error( f"文件夹不存在: {folder_path}" )
			return json_data_list

		# UI显示简洁信息
		self.__log_output.append( f"开始读取JSON文件", Colors.GREEN )
		self.__logger.info( f"开始从 {folder_path} 读取JSON文件" )

		try:
			# 获取所有JSON文件
			json_files = [ f for f in os.listdir( folder_path ) if f.endswith( '.json' ) ]
			total_files = len( json_files )

			if total_files == 0:
				# UI显示简洁警告
				self.__log_output.append( f"未找到JSON文件", Colors.ORANGE )
				self.__logger.warning( f"在 {folder_path} 中未找到JSON文件" )
				return json_data_list

			self.__log_output.append( f"找到 {total_files} 个JSON文件", Colors.GREEN )
			self.__logger.info( f"找到 {total_files} 个JSON文件" )

			# 读取所有JSON文件
			for i, json_file in enumerate( json_files ):
				if i % 100 == 0:  # 每处理100个文件更新一次进度
					# UI显示简洁进度
					self.__log_output.append( f"处理进度: {i}/{total_files}", Colors.GREEN )
					self.__logger.info( f"正在处理: {i}/{total_files} JSON文件" )

				file_path = os.path.join( folder_path, json_file )
				try:
					with open( file_path, 'r', encoding='utf-8' ) as f:
						data = json.load( f )
						json_data_list.append(
							{
								'data': data,
								'file_path': file_path
							}
						)
				except Exception as e:
					self.__log_output.append( f"读取JSON文件 {json_file} 失败: {str( e )}", Colors.ORANGE )
					self.__logger.warning( f"读取JSON文件 {json_file} 失败: {str( e )}" )
					continue

			# UI显示简洁结果
			self.__log_output.append( f"读取完成: {len( json_data_list )} 个文件", Colors.GREEN )
			self.__logger.info( f"成功读取 {len( json_data_list )} 个JSON文件" )
			return json_data_list
		except Exception as e:
			self.__log_output.append( f"读取JSON文件失败: {str( e )}", Colors.RED )
			self.__logger.error( f"读取JSON文件失败: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return json_data_list

	def __filter_multilabel_data( self, json_data_list: List[ Dict[ str, Any ] ], expected_labels: int ) -> List[
		Dict[ str, Any ] ]:
		"""
		过滤掉标签数量大于等于预期值的数据

		参数:
			json_data_list: JSON数据列表
			expected_labels: 预期的标签数量

		返回:
			过滤后的JSON数据列表
		"""
		if expected_labels <= 0:
			return json_data_list

		filtered_data = [ ]
		removed_count = 0

		for item in json_data_list:
			data = item[ 'data' ]
			shapes = data.get( 'shapes', [ ] )

			# 计算不同标签的数量
			labels = set( shape.get( 'label', '' ) for shape in shapes )
			if len( labels ) >= expected_labels:
				removed_count += 1
				# 删除JSON文件和对应的图像文件
				try:
					# 获取图像文件路径
					image_path = data.get( 'imagePath', '' )
					if image_path:
						json_dir = os.path.dirname( item[ 'file_path' ] )
						image_full_path = os.path.join( json_dir, image_path )

						# 删除图像文件(如果存在)
						if os.path.exists( image_full_path ):
							os.remove( image_full_path )

					# 删除JSON文件
					os.remove( item[ 'file_path' ] )
				except Exception as e:
					self.__log_output.append( f"删除文件失败: {str( e )}", Colors.ORANGE )
					self.__logger.warning( f"删除文件失败: {str( e )}" )
			else:
				filtered_data.append( item )

		# UI显示简洁信息
		self.__log_output.append( f"过滤多标签数据: {removed_count} 个", Colors.GREEN )
		self.__logger.info( f"过滤掉 {removed_count} 个多标签数据（标签数量 >= {expected_labels}）" )
		return filtered_data

	def __split_data( self, json_data_list: List[ Dict[ str, Any ] ], train_ratio: float, pretrain_ratio: float ) -> \
			Dict[ str, Any ]:
		"""
		根据比例划分训练数据和预训练数据，为每条数据添加标识

		新的逻辑：
		1. 为每条数据添加标识字段，避免数据重复
		2. 标识字段：pretrain_train, pretrain_val, train, val
		3. 每条数据只保存一次，通过标识区分用途
		4. 消除数据重复，确保数据库中每条记录只保存一次

		参数:
			json_data_list: JSON数据列表
			train_ratio: 训练集比例(0-1之间的浮点数)
			pretrain_ratio: 预训练数据比例(0-1之间的浮点数)

		返回:
			包含标识数据和统计信息的字典：
			{
				"all_data": [带标识的数据列表],
				"statistics": {
					"total": 总数据量,
					"train": 训练数据量,
					"val": 验证数据量,
					"pretrain_train": 预训练训练数据量,
					"pretrain_val": 预训练验证数据量
				}
			}
		"""
		import random
		random.seed( 42 )  # 固定随机种子，确保结果可复现

		# 打乱数据
		shuffled_data = json_data_list.copy()
		random.shuffle( shuffled_data )

		total_count = len( shuffled_data )
		if total_count == 0:
			return {
				"all_data": [ ],
				"statistics": {
					"total": 0, "train": 0, "val": 0,
					"pretrain_train": 0, "pretrain_val": 0
				}
			}

		# 特殊处理：当JSON文件数量小于等于20个时
		if total_count <= 20:
			# UI显示简洁信息
			self.__log_output.append( f"小数据集模式: {total_count} 个文件", Colors.GREEN )
			self.__logger.info( f"检测到JSON文件数量为 {total_count} 个（≤20），所有数据将同时作为预训练和真实训练数据" )

			# 根据训练比例划分训练集和验证集
			train_count = int( total_count * train_ratio )

			# 为每条数据添加标识
			for i, data in enumerate( shuffled_data ):
				# 初始化所有标识为false
				data[ "pretrain_train" ] = "false"
				data[ "pretrain_val" ] = "false"
				data[ "train" ] = "false"
				data[ "val" ] = "false"

				if i < train_count:
					# 训练数据：同时是预训练和真实训练数据
					data[ "pretrain_train" ] = "true"
					data[ "train" ] = "true"
				else:
					# 验证数据：同时是预训练和真实验证数据
					data[ "pretrain_val" ] = "true"
					data[ "val" ] = "true"

			# 统计信息
			val_count = total_count - train_count

			# UI显示简洁的划分结果
			self.__log_output.append( f"数据划分完成: 训练{train_count}条, 验证{val_count}条", Colors.GREEN )
			# 控制台记录详细信息
			self.__logger.info( f"数据划分完成:" )
			self.__logger.info( f"  - 总数据: {total_count} 条" )
			self.__logger.info( f"  - 预训练数据与真实训练数据相同: {total_count} 条" )
			self.__logger.info( f"  - 训练集: {train_count} 条, 验证集: {val_count} 条" )

			return {
				"all_data": shuffled_data,
				"statistics": {
					"total": total_count,
					"train": train_count,
					"val": val_count,
					"pretrain_train": train_count,
					"pretrain_val": val_count
				}
			}

		# 改进后的处理逻辑（数据量>20时）
		# 步骤1: 计算预训练数据数量
		pretrain_count = int( total_count * pretrain_ratio )

		# 步骤2: 计算各类数据的数量
		pretrain_train_count = int( pretrain_count * train_ratio )
		pretrain_val_count = pretrain_count - pretrain_train_count

		additional_count = total_count - pretrain_count
		additional_train_count = int( additional_count * train_ratio )
		additional_val_count = additional_count - additional_train_count

		# 总的训练和验证数据量
		total_train_count = pretrain_train_count + additional_train_count
		total_val_count = pretrain_val_count + additional_val_count

		# 步骤3: 为每条数据添加标识
		for i, data in enumerate( shuffled_data ):
			# 初始化所有标识为false
			data[ "pretrain_train" ] = "false"
			data[ "pretrain_val" ] = "false"
			data[ "train" ] = "false"
			data[ "val" ] = "false"

			if i < pretrain_train_count:
				# 预训练训练数据：同时是预训练和真实训练数据
				data[ "pretrain_train" ] = "true"
				data[ "train" ] = "true"
			elif i < pretrain_count:
				# 预训练验证数据：同时是预训练和真实验证数据
				data[ "pretrain_val" ] = "true"
				data[ "val" ] = "true"
			elif i < pretrain_count + additional_train_count:
				# 额外训练数据：只是真实训练数据
				data[ "train" ] = "true"
			else:
				# 额外验证数据：只是真实验证数据
				data[ "val" ] = "true"

		# UI显示简洁的划分结果
		self.__log_output.append(
			f"数据划分完成: 总{total_count}条, 训练{total_train_count}条, 验证{total_val_count}条", Colors.GREEN
		)
		# 控制台记录详细的划分信息
		self.__logger.info( f"数据划分完成:" )
		self.__logger.info( f"  - 总数据: {total_count} 条" )
		self.__logger.info(
			f"  - 预训练数据: {pretrain_count} 条 (训练: {pretrain_train_count}, 验证: {pretrain_val_count})"
		)
		self.__logger.info(
			f"  - 额外训练数据: {additional_count} 条 (训练: {additional_train_count}, 验证: {additional_val_count})"
		)
		self.__logger.info( f"  - 真实训练数据: {total_count} 条 (训练: {total_train_count}, 验证: {total_val_count})" )
		self.__logger.info( f"  - 数据无重复: 每条数据只保存一次，通过标识区分用途" )

		return {
			"all_data": shuffled_data,
			"statistics": {
				"total": total_count,
				"train": total_train_count,
				"val": total_val_count,
				"pretrain_train": pretrain_train_count,
				"pretrain_val": pretrain_val_count
			}
		}

	def __get_folder_for_image( self, output_folder: str ) -> str:
		"""
		获取图像应该保存的子文件夹路径，采用性能最优的方案

		参数:
			output_folder: 输出文件夹的根路径

		返回:
			子文件夹的完整路径
		"""
		with self.__counter_lock:
			# 初始化文件夹状态
			if self.__current_folder_index is None:
				self.__initialize_folder_state( output_folder )

			# 此时self.__current_folder_index一定不为None
			assert self.__current_folder_index is not None

			# 检查当前文件夹是否已达到10000张图像
			if self.__current_folder_count >= 10000:
				# 需要创建新文件夹
				self.__current_folder_index = self.__current_folder_index + 1  # 避免使用+=运算符
				self.__current_folder_count = 0

				# 更新文件夹缓存
				folder_start = self.__current_folder_index * 10000
				folder_end = folder_start + 9999
				subfolder_name = f"{folder_start}-{folder_end}"
				self.__folder_cache[ self.__current_folder_index ] = 0  # 新文件夹计数为0

			# 当前文件夹路径
			folder_start = self.__current_folder_index * 10000
			folder_end = folder_start + 9999
			subfolder_name = f"{folder_start}-{folder_end}"
			subfolder_path = os.path.join( output_folder, subfolder_name )

			# 如果子文件夹不存在，创建它
			os.makedirs( subfolder_path, exist_ok=True )

			# 增加当前文件夹计数
			self.__current_folder_count += 1
			self.__folder_cache[ self.__current_folder_index ] = self.__current_folder_count

			return subfolder_path

	def __initialize_folder_state( self, output_folder: str ) -> None:
		"""
		初始化文件夹状态，找出最后一个子文件夹并计算其中的文件数量

		参数:
			output_folder: 输出文件夹的根路径
		"""
		try:
			# 确保输出文件夹存在
			os.makedirs( output_folder, exist_ok=True )

			# 先找出所有符合命名规则的子文件夹
			valid_folders = [ ]
			for item in os.listdir( output_folder ):
				folder_path = os.path.join( output_folder, item )
				if os.path.isdir( folder_path ) and '-' in item:
					try:
						start, _ = item.split( '-' )  # 只需要start部分
						folder_index = int( start ) // 10000
						valid_folders.append( (folder_index, folder_path) )
					except (ValueError, TypeError):
						continue

			if not valid_folders:
				# 没有符合规则的子文件夹，创建第一个
				self.__current_folder_index = 0
				self.__current_folder_count = 0
				self.__folder_cache[ 0 ] = 0
				# 只在控制台记录技术细节
				self.__logger.info( "未找到现有子文件夹，从0开始" )
				return

			# 找出索引最大的子文件夹
			valid_folders.sort( key=lambda x: x[ 0 ], reverse=True )
			max_index, last_folder = valid_folders[ 0 ]

			# 计算最后一个子文件夹中的文件数量
			image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff', '.gif')
			file_count = sum(
				1 for f in os.listdir( last_folder )
				if os.path.isfile( os.path.join( last_folder, f ) )
				and f.lower().endswith( image_extensions )
			)

			# 设置当前状态
			self.__current_folder_index = max_index
			self.__current_folder_count = file_count
			self.__folder_cache[ max_index ] = file_count

			# 检查是否需要创建新文件夹
			if file_count >= 10000:
				self.__current_folder_index += 1
				self.__current_folder_count = 0
				self.__folder_cache[ self.__current_folder_index ] = 0

			# 只在控制台记录技术细节
			self.__logger.info(
				f"从现有子文件夹恢复，当前文件夹索引: {self.__current_folder_index}, 已有文件数: {self.__current_folder_count}"
			)

		except Exception as e:
			# 出错时使用默认值
			self.__log_output.append( f"初始化文件夹状态时出错: {str( e )}", Colors.RED )
			self.__logger.error( f"初始化文件夹状态时出错: {str( e )}" )
			self.__log_output.append( traceback.format_exc(), Colors.RED )
			self.__logger.error( traceback.format_exc() )
			self.__current_folder_index = 0
			self.__current_folder_count = 0
			self.__folder_cache[ 0 ] = 0

	def __save_image( self, json_data: Dict[ str, Any ], json_file_path: str, output_folder: str ) -> Optional[ str ]:
		"""
		将图像文件保存到输出文件夹

		改进后的逻辑：
		1. 统一处理绝对路径和相对路径
		2. 在保存前详细检查原始图像是否存在
		3. 提供详细的日志信息用于调试

		参数:
			json_data: JSON数据
			json_file_path: JSON文件路径
			output_folder: 输出文件夹路径

		返回:
			成功时返回保存后的图像路径，失败时返回None
		"""
		try:
			# 步骤1: 获取和验证图像路径
			image_path = json_data.get( 'imagePath', '' )
			if not image_path:
				self.__log_output.append( f"图像路径获取失败", Colors.RED )
				self.__logger.error( f"图像路径获取失败" )
				self.__log_output.append( f"  - JSON文件: {json_file_path}", Colors.RED )
				self.__logger.error( f"  - JSON文件: {json_file_path}" )
				self.__log_output.append( f"  - 错误原因: JSON文件中未找到imagePath字段", Colors.RED )
				self.__logger.error( f"  - 错误原因: JSON文件中未找到imagePath字段" )
				self.__log_output.append( f"  - 建议: 请检查JSON文件格式是否正确", Colors.RED )
				self.__logger.error( f"  - 建议: 请检查JSON文件格式是否正确" )
				return None

			# 获取JSON文件相关信息
			json_dir = os.path.dirname( json_file_path )
			json_basename = os.path.splitext( os.path.basename( json_file_path ) )[ 0 ]
			image_basename = os.path.splitext( os.path.basename( image_path ) )[ 0 ]

			# UI只显示关键信息
			self.__log_output.append( f"处理图像保存", Colors.GREEN )
			# 控制台记录详细信息
			self.__logger.info( f"开始处理图像保存" )
			self.__logger.info( f"  - JSON文件: {json_file_path}" )
			self.__logger.info( f"  - 原始图像路径: {image_path}" )
			self.__logger.info( f"  - JSON目录: {json_dir}" )

			# 步骤2: 检查JSON文件名与图像文件名是否匹配
			if json_basename != image_basename:
				self.__log_output.append(
					f"文件名不匹配: JSON({json_basename}) vs 图像({image_basename})", Colors.ORANGE
				)
				self.__logger.warning( f"文件名不匹配: JSON({json_basename}) vs 图像({image_basename})" )
				# 尝试在同目录下查找与JSON同名的图像文件
				found_matching_image = False
				for ext in [ '.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff' ]:
					potential_image_path = os.path.join( json_dir, json_basename + ext )
					if os.path.exists( potential_image_path ):
						# 只在控制台记录详细路径信息
						self.__logger.info( f"找到匹配的图像文件: {potential_image_path}" )
						image_path = json_basename + ext
						found_matching_image = True
						break

				if not found_matching_image:
					self.__log_output.append(
						f"未找到与JSON文件名匹配的图像文件，继续使用原始路径: {image_path}", Colors.ORANGE
					)
					self.__logger.warning( f"未找到与JSON文件名匹配的图像文件，继续使用原始路径: {image_path}" )

			# 步骤3: 构建完整的图像路径
			if not os.path.isabs( image_path ):
				# 处理相对路径
				if os.path.dirname( image_path ) == '':
					# image_path只是文件名，直接与JSON目录拼接
					image_full_path = os.path.join( json_dir, image_path )
					# 只在控制台记录路径处理详情
					self.__logger.info( f"相对路径处理: {image_path} → {image_full_path}" )
				else:
					# image_path含有路径部分，正常拼接
					image_full_path = os.path.join( json_dir, image_path )
					# 只在控制台记录路径拼接详情
					self.__logger.info( f"相对路径拼接: {json_dir} + {image_path} → {image_full_path}" )
			else:
				# 处理绝对路径
				image_full_path = image_path
				# 只在控制台记录路径信息
				self.__logger.info( f"使用绝对路径: {image_full_path}" )

			# 步骤4: 详细的图像存在性检查（核心改进）
			if not os.path.exists( image_full_path ):
				self.__log_output.append( f"图像文件不存在，无法保存", Colors.RED )
				self.__logger.error( f"图像文件不存在，无法保存" )
				self.__log_output.append( f"  - JSON文件: {json_file_path}", Colors.RED )
				self.__logger.error( f"  - JSON文件: {json_file_path}" )
				self.__log_output.append( f"  - 原始图像路径: {image_path}", Colors.RED )
				self.__logger.error( f"  - 原始图像路径: {image_path}" )
				self.__log_output.append( f"  - 查找的完整路径: {image_full_path}", Colors.RED )
				self.__logger.error( f"  - 查找的完整路径: {image_full_path}" )
				self.__log_output.append( f"  - JSON目录: {json_dir}", Colors.RED )
				self.__logger.error( f"  - JSON目录: {json_dir}" )
				self.__log_output.append(
					f"  - 路径类型: {'绝对路径' if os.path.isabs( image_path ) else '相对路径'}", Colors.RED
				)
				self.__logger.error( f"  - 路径类型: {'绝对路径' if os.path.isabs( image_path ) else '相对路径'}" )
				self.__log_output.append( f"  - 建议: 请检查图像文件是否存在于指定位置", Colors.RED )
				self.__logger.error( f"  - 建议: 请检查图像文件是否存在于指定位置" )
				return None

			# 图像存在性检查通过，只在控制台记录详细路径
			self.__logger.info( f"图像文件检查通过: {image_full_path}" )

			# 步骤5: 处理文件扩展名
			image_extension = os.path.splitext( image_path )[ 1 ]
			if not image_extension:
				# 如果没有扩展名，尝试从文件内容猜测
				import imghdr
				image_type = imghdr.what( image_full_path )
				if image_type:
					image_extension = f".{image_type}"
					# 只在控制台记录技术细节
					self.__logger.info( f"从文件内容检测到扩展名: {image_extension}" )
				else:
					image_extension = ".jpg"  # 默认使用jpg扩展名
					self.__log_output.append( f"无法检测文件类型，使用默认扩展名: {image_extension}", Colors.ORANGE )
					self.__logger.warning( f"无法检测文件类型，使用默认扩展名: {image_extension}" )

			# 构建标准化的图像文件名（与JSON文件名相同，但保留原扩展名）
			image_filename = json_basename + image_extension

			# 步骤6: 获取输出子文件夹并保存
			subfolder_path = self.__get_folder_for_image( output_folder )
			target_path = os.path.join( subfolder_path, image_filename )

			# 复制图像文件
			shutil.copy2( image_full_path, target_path )

			# UI显示简洁信息，控制台记录详细路径
			self.__log_output.append( f"图像保存成功", Colors.GREEN )
			self.__logger.info( f"图像保存成功: {target_path}" )
			return target_path

		except Exception as e:
			self.__log_output.append( f"保存图像时发生异常", Colors.RED )
			self.__logger.error( f"保存图像时发生异常" )
			self.__log_output.append( f"  - JSON文件: {json_file_path}", Colors.RED )
			self.__logger.error( f"  - JSON文件: {json_file_path}" )
			self.__log_output.append( f"  - 异常信息: {str( e )}", Colors.RED )
			self.__logger.error( f"  - 异常信息: {str( e )}" )
			self.__log_output.append( f"  - 建议: 请检查文件权限和磁盘空间", Colors.RED )
			self.__logger.error( f"  - 建议: 请检查文件权限和磁盘空间" )
			return None

	def __check_shapely_available( self ) -> bool:
		"""
		检查 shapely 库是否可用
		"""
		try:
			import shapely
			return True
		except ImportError:
			self.__log_output.append( "警告: shapely库未安装，无法计算坐标重叠率", Colors.ORANGE )
			self.__logger.warning( "shapely库未安装，无法计算坐标重叠率" )
			return False

	def __calculate_coordinate_position_similarity( self, points1: List[ List[ float ] ], points2: List[ List[ float ] ] ) -> float:
		"""
		计算坐标位置相似度

		权重分配：
		- 点对点距离：50%
		- 重心位置：20%
		- 边界框重叠：15%
		- 尺度相似度：15%

		参数:
			points1: 第一个坐标集合
			points2: 第二个坐标集合

		返回:
			坐标位置相似度 (0.0-1.0)
		"""
		try:
			import numpy as np

			if not points1 or not points2:
				return 0.0

			points1_np = np.array( points1 )
			points2_np = np.array( points2 )

			# 1. 点对点距离相似度 (权重50%)
			point_distance_sim = self.__calculate_point_to_point_distance_similarity( points1_np, points2_np )

			# 2. 重心位置相似度 (权重20%)
			centroid_sim = self.__calculate_centroid_position_similarity( points1_np, points2_np )

			# 3. 边界框重叠相似度 (权重15%)
			bbox_overlap_sim = self.__calculate_bbox_overlap_similarity( points1_np, points2_np )

			# 4. 尺度相似度 (权重15%)
			scale_sim = self.__calculate_scale_similarity( points1_np, points2_np )

			# 综合坐标位置相似度
			coordinate_similarity = (
				0.5 * point_distance_sim +    # 点对点距离权重50%
				0.2 * centroid_sim +          # 重心位置权重20%
				0.15 * bbox_overlap_sim +     # 边界框重叠权重15%
				0.15 * scale_sim              # 尺度相似度权重15%
			)

			self.__logger.debug(
				f"坐标位置相似度详情: 点距离={point_distance_sim:.3f}, 重心={centroid_sim:.3f}, "
				f"边界框={bbox_overlap_sim:.3f}, 尺度={scale_sim:.3f}, 综合={coordinate_similarity:.3f}"
			)

			return coordinate_similarity

		except Exception as e:
			self.__logger.warning( f"坐标位置相似度计算失败: {str( e )}" )
			return 0.0

	def __calculate_geometric_shape_similarity( self, points1: List[ List[ float ] ], points2: List[ List[ float ] ] ) -> float:
		"""
		计算几何形状相似度

		使用shapely库进行精确几何计算：
		- IoU (Intersection over Union)：交集面积/并集面积
		- IoS (Intersection over Smaller)：交集面积/较小面积
		- 归一化形状IoU：消除大小影响的纯形状比较

		参数:
			points1: 第一个坐标集合
			points2: 第二个坐标集合

		返回:
			几何形状相似度 (0.0-1.0)
		"""
		try:
			from shapely.geometry import Polygon
			from shapely.errors import TopologicalError

			if not points1 or not points2:
				return 0.0

			# 创建shapely多边形对象
			poly1 = Polygon( points1 )
			poly2 = Polygon( points2 )

			# 确保多边形有效
			if not poly1.is_valid:
				poly1 = poly1.buffer( 0 )
			if not poly2.is_valid:
				poly2 = poly2.buffer( 0 )

			# 1. 计算IoU (权重40%)
			iou = self.__calculate_polygon_iou( poly1, poly2 )

			# 2. 计算IoS (权重30%)
			ios = self.__calculate_polygon_ios( poly1, poly2 )

			# 3. 计算归一化形状IoU (权重30%)
			normalized_iou = self.__calculate_normalized_shape_iou( poly1, poly2 )

			# 综合几何形状相似度
			geometric_similarity = (
				0.4 * iou +              # IoU权重40%
				0.3 * ios +              # IoS权重30%
				0.3 * normalized_iou     # 归一化形状IoU权重30%
			)

			self.__logger.debug(
				f"几何形状相似度详情: IoU={iou:.3f}, IoS={ios:.3f}, "
				f"归一化IoU={normalized_iou:.3f}, 综合={geometric_similarity:.3f}"
			)

			return geometric_similarity

		except (TopologicalError, Exception) as e:
			self.__logger.warning( f"几何形状相似度计算失败: {str( e )}" )
			# shapely库不可用时，使用简化的边界框IoU作为降级方案
			return self.__calculate_bbox_overlap_similarity(
				__import__('numpy').array( points1 ),
				__import__('numpy').array( points2 )
			)

	def __calculate_point_to_point_distance_similarity( self, points1_np, points2_np ) -> float:
		"""
		计算点对点距离相似度

		方法：计算对应点之间的欧几里得距离，归一化到坐标范围
		"""
		try:
			import numpy as np

			# 如果点数不同，使用最近点匹配
			if len( points1_np ) == len( points2_np ):
				# 点数相同，直接对应计算
				distances = np.linalg.norm( points1_np - points2_np, axis=1 )
				avg_distance = np.mean( distances )
			else:
				# 点数不同，计算最小距离匹配
				min_distances = []
				for p1 in points1_np:
					distances_to_p1 = [np.linalg.norm( p1 - p2 ) for p2 in points2_np]
					min_distances.append( min( distances_to_p1 ) )

				for p2 in points2_np:
					distances_to_p2 = [np.linalg.norm( p2 - p1 ) for p1 in points1_np]
					min_distances.append( min( distances_to_p2 ) )

				avg_distance = np.mean( min_distances )

			# 归一化距离到坐标范围
			all_points = np.vstack( [points1_np, points2_np] )
			coord_range = max(
				np.max( all_points ) - np.min( all_points ),
				1.0  # 避免除零
			)

			normalized_distance = avg_distance / coord_range

			# 转换为相似度：距离越小，相似度越高
			point_distance_similarity = 1.0 / (1.0 + normalized_distance)

			return float( point_distance_similarity )

		except Exception:
			return 0.0

	def __calculate_centroid_position_similarity( self, points1_np, points2_np ) -> float:
		"""
		计算重心位置相似度

		方法：比较两个标注的几何中心，相对于标注尺寸进行归一化
		"""
		try:
			import numpy as np

			# 计算重心
			centroid1 = np.mean( points1_np, axis=0 )
			centroid2 = np.mean( points2_np, axis=0 )

			# 重心距离
			centroid_distance = np.linalg.norm( centroid1 - centroid2 )

			# 计算标注的特征尺寸（用于归一化）
			bbox1_size = max(
				np.max( points1_np[:, 0] ) - np.min( points1_np[:, 0] ),
				np.max( points1_np[:, 1] ) - np.min( points1_np[:, 1] ),
				1.0
			)
			bbox2_size = max(
				np.max( points2_np[:, 0] ) - np.min( points2_np[:, 0] ),
				np.max( points2_np[:, 1] ) - np.min( points2_np[:, 1] ),
				1.0
			)

			# 大小相似度检查（严格的大小差异处理）
			size_ratio = min( bbox1_size, bbox2_size ) / max( bbox1_size, bbox2_size )

			# 如果大小差异超过10倍，直接返回低相似度
			if size_ratio < 0.1:
				return 0.0

			# 使用较大的尺寸进行归一化
			reference_size = max( bbox1_size, bbox2_size )
			normalized_centroid_distance = centroid_distance / reference_size

			# 转换为相似度（结合位置和大小）
			position_similarity = 1.0 / (1.0 + normalized_centroid_distance)
			centroid_similarity = 0.4 * position_similarity + 0.6 * size_ratio  # 增加大小权重

			return float( centroid_similarity )

		except Exception:
			return 0.0

	def __calculate_bbox_overlap_similarity( self, points1_np, points2_np ) -> float:
		"""
		计算边界框重叠相似度

		方法：计算最小外接矩形的IoU
		"""
		try:
			import numpy as np

			# 计算边界框
			bbox1 = [
				np.min( points1_np[:, 0] ),  # min_x
				np.min( points1_np[:, 1] ),  # min_y
				np.max( points1_np[:, 0] ),  # max_x
				np.max( points1_np[:, 1] )   # max_y
			]

			bbox2 = [
				np.min( points2_np[:, 0] ),  # min_x
				np.min( points2_np[:, 1] ),  # min_y
				np.max( points2_np[:, 0] ),  # max_x
				np.max( points2_np[:, 1] )   # max_y
			]

			# 计算交集
			intersection_min_x = max( bbox1[0], bbox2[0] )
			intersection_min_y = max( bbox1[1], bbox2[1] )
			intersection_max_x = min( bbox1[2], bbox2[2] )
			intersection_max_y = min( bbox1[3], bbox2[3] )

			# 检查是否有交集
			if intersection_min_x >= intersection_max_x or intersection_min_y >= intersection_max_y:
				return 0.0

			# 计算面积
			intersection_area = (intersection_max_x - intersection_min_x) * (intersection_max_y - intersection_min_y)

			bbox1_area = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
			bbox2_area = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])

			union_area = bbox1_area + bbox2_area - intersection_area

			if union_area <= 0:
				return 0.0

			# 边界框IoU
			bbox_iou = intersection_area / union_area

			return bbox_iou

		except Exception:
			return 0.0

	def __calculate_scale_similarity( self, points1_np, points2_np ) -> float:
		"""
		计算尺度相似度

		方法：比较标注的整体大小，使用min/max比例
		"""
		try:
			import numpy as np

			# 计算边界框尺寸
			bbox1_width = np.max( points1_np[:, 0] ) - np.min( points1_np[:, 0] )
			bbox1_height = np.max( points1_np[:, 1] ) - np.min( points1_np[:, 1] )
			bbox1_size = max( bbox1_width, bbox1_height )

			bbox2_width = np.max( points2_np[:, 0] ) - np.min( points2_np[:, 0] )
			bbox2_height = np.max( points2_np[:, 1] ) - np.min( points2_np[:, 1] )
			bbox2_size = max( bbox2_width, bbox2_height )

			if bbox1_size == 0 or bbox2_size == 0:
				return 0.0

			# 尺寸比例相似度
			size_ratio = min( bbox1_size, bbox2_size ) / max( bbox1_size, bbox2_size )

			return float( size_ratio )

		except Exception:
			return 0.0

	def __calculate_polygon_iou( self, poly1, poly2 ) -> float:
		"""
		计算多边形的IoU (Intersection over Union)

		方法：交集面积 / 并集面积
		"""
		try:
			# 计算交集和并集
			intersection = poly1.intersection( poly2 )
			union = poly1.union( poly2 )

			# 获取面积
			intersection_area = intersection.area
			union_area = union.area

			if union_area <= 0:
				return 0.0

			iou = intersection_area / union_area
			return float( iou )

		except Exception:
			return 0.0

	def __calculate_polygon_ios( self, poly1, poly2 ) -> float:
		"""
		计算多边形的IoS (Intersection over Smaller)

		方法：交集面积 / 较小多边形面积
		"""
		try:
			# 计算交集
			intersection = poly1.intersection( poly2 )
			intersection_area = intersection.area

			# 获取较小的面积
			area1 = poly1.area
			area2 = poly2.area
			smaller_area = min( area1, area2 )

			if smaller_area <= 0:
				return 0.0

			ios = intersection_area / smaller_area
			return float( ios )

		except Exception:
			return 0.0

	def __calculate_normalized_shape_iou( self, poly1, poly2 ) -> float:
		"""
		计算归一化形状IoU

		方法：消除位置和大小影响，只比较纯形状
		"""
		try:
			# 归一化多边形
			norm_poly1 = self.__normalize_polygon_for_shape_comparison( poly1 )
			norm_poly2 = self.__normalize_polygon_for_shape_comparison( poly2 )

			# 计算归一化后的IoU
			intersection = norm_poly1.intersection( norm_poly2 )
			union = norm_poly1.union( norm_poly2 )

			intersection_area = intersection.area
			union_area = union.area

			if union_area <= 0:
				return 0.0

			normalized_iou = intersection_area / union_area
			return float( normalized_iou )

		except Exception:
			return 0.0

	def __normalize_polygon_for_shape_comparison( self, poly ):
		"""
		归一化多边形用于形状比较

		方法：移动到原点，缩放到单位面积
		"""
		try:
			import numpy as np
			from shapely.geometry import Polygon

			# 获取重心
			centroid = poly.centroid

			# 移动到原点
			coords = list( poly.exterior.coords )
			translated_coords = [(x - centroid.x, y - centroid.y) for x, y in coords]

			# 创建移动后的多边形
			translated_poly = Polygon( translated_coords )

			# 缩放到单位面积
			current_area = translated_poly.area
			if current_area > 0:
				scale_factor = 1.0 / np.sqrt( current_area )
				scaled_coords = [(x * scale_factor, y * scale_factor) for x, y in translated_coords]
				normalized_poly = Polygon( scaled_coords )
			else:
				normalized_poly = translated_poly

			return normalized_poly

		except Exception:
			return poly

	def __calculate_comprehensive_similarity_and_decision(
			self, points1: List[ List[ float ] ], points2: List[ List[ float ] ]
	) -> Dict[ str, Any ]:
		"""
		计算综合相似度并做出决策

		实现四层决策结构：
		1. 极高相似度检查 (Early Exit)
		2. 综合相似度评估
		3. 双重验证
		4. 单一维度高相似度
		5. 追加判断 (30%严格阈值)

		参数:
			points1: 第一个坐标集合
			points2: 第二个坐标集合

		返回:
			包含相似度详情和决策结果的字典
		"""
		try:
			if not points1 or not points2:
				return {
					'coordinate_similarity': 0.0,
					'geometric_similarity': 0.0,
					'shape_feature_similarity': 0.0,
					'point_sequence_similarity': 0.0,
					'comprehensive_similarity': 0.0,
					'decision': 'add',
					'reason': '坐标为空，可以追加',
					'layer': 'empty_check'
				}

			# 计算四个维度的相似度
			coordinate_sim = self.__calculate_coordinate_position_similarity( points1, points2 )
			geometric_sim = self.__calculate_geometric_shape_similarity( points1, points2 )
			shape_feature_sim = self.__calculate_shape_feature_similarity( points1, points2 )
			point_sequence_sim = self.__calculate_point_sequence_similarity( points1, points2 )

			# 计算综合相似度（使用配置的权重）
			comprehensive_sim = (
					self.__coordinate_weight * coordinate_sim +      # 坐标权重
					self.__geometric_weight * geometric_sim +        # 几何权重
					self.__shape_feature_weight * shape_feature_sim + # 特征权重
					self.__point_sequence_weight * point_sequence_sim # 序列权重
			)

			# 四层决策机制
			decision_result = self.__make_layered_similarity_decision(
				coordinate_sim, geometric_sim, comprehensive_sim
			)

			return {
				'coordinate_similarity': coordinate_sim,
				'geometric_similarity': geometric_sim,
				'shape_feature_similarity': shape_feature_sim,
				'point_sequence_similarity': point_sequence_sim,
				'comprehensive_similarity': comprehensive_sim,
				'decision': decision_result[ 'decision' ],
				'reason': decision_result[ 'reason' ],
				'layer': decision_result[ 'layer' ]
			}

		except Exception as e:
			self.__logger.warning( f"综合相似度计算失败: {str( e )}" )
			return {
				'coordinate_similarity': 0.0,
				'geometric_similarity': 0.0,
				'shape_feature_similarity': 0.0,
				'point_sequence_similarity': 0.0,
				'comprehensive_similarity': 0.0,
				'decision': 'add',
				'reason': f'计算失败，默认追加: {str( e )}',
				'layer': 'error'
			}

	def __make_layered_similarity_decision(
			self, coordinate_sim: float, geometric_sim: float,
			comprehensive_sim: float
	) -> Dict[ str, str ]:
		"""
		实现分层决策机制

		四层决策结构（严格阈值，30%追加标准）
		"""
		try:
			# 第一层：极高相似度检查 (Early Exit)
			if coordinate_sim >= self.__coordinate_extreme_threshold or geometric_sim >= self.__geometric_extreme_threshold:
				return {
					'decision': 'skip',
					'reason': f'极高相似度 (坐标={coordinate_sim:.3f}, 几何={geometric_sim:.3f})',
					'layer': 'layer_1_extreme'
				}

			# 第二层：综合相似度评估
			if comprehensive_sim >= self.__comprehensive_high_threshold:
				return {
					'decision': 'skip',
					'reason': f'综合相似度过高 ({comprehensive_sim:.3f})',
					'layer': 'layer_2_comprehensive'
				}

			# 第三层：双重验证
			if coordinate_sim >= self.__coordinate_double_threshold and geometric_sim >= self.__geometric_double_threshold:
				return {
					'decision': 'skip',
					'reason': f'坐标和几何都相似 (坐标={coordinate_sim:.3f}, 几何={geometric_sim:.3f})',
					'layer': 'layer_3_double_verification'
				}

			# 第四层：单一维度高相似度
			if coordinate_sim >= self.__coordinate_single_threshold:
				return {
					'decision': 'skip',
					'reason': f'坐标高度相似 ({coordinate_sim:.3f})',
					'layer': 'layer_4_coordinate_high'
				}

			if geometric_sim >= self.__geometric_single_threshold:
				return {
					'decision': 'skip',
					'reason': f'几何高度相似 ({geometric_sim:.3f})',
					'layer': 'layer_4_geometric_high'
				}

			# 第五层：追加判断（严格阈值）
			if comprehensive_sim <= self.__comprehensive_add_threshold:
				return {
					'decision': 'add',
					'reason': f'相似度低，可以追加 (综合={comprehensive_sim:.3f})',
					'layer': 'layer_5_add_allowed'
				}
			else:
				return {
					'decision': 'skip',
					'reason': f'相似度超过30%阈值 (综合={comprehensive_sim:.3f})',
					'layer': 'layer_5_add_denied'
				}

		except Exception as e:
			return {
				'decision': 'add',
				'reason': f'决策失败，默认追加: {str( e )}',
				'layer': 'error'
			}

	def __calculate_point_sequence_similarity(
			self, points1: List[ List[ float ] ], points2: List[ List[ float ] ]
	) -> float:
		"""
		计算点序列相似度

		评估方面：
		- 点数匹配：标注点数量的相似程度
		- 分布模式：相邻点角度变化的相关性

		参数:
			points1: 第一个坐标集合
			points2: 第二个坐标集合

		返回:
			点序列相似度 (0.0-1.0)
		"""
		try:
			if not points1 or not points2:
				return 0.0

			# 1. 点数匹配相似度 (权重60%)
			point_count_sim = self.__calculate_point_count_matching( points1, points2 )

			# 2. 分布模式相似度 (权重40%)
			distribution_sim = self.__calculate_distribution_pattern_similarity( points1, points2 )

			# 综合点序列相似度
			sequence_similarity = (
					0.6 * point_count_sim +  # 点数匹配权重60%
					0.4 * distribution_sim  # 分布模式权重40%
			)

			self.__logger.debug(
				f"点序列相似度详情: 点数匹配={point_count_sim:.3f}, 分布模式={distribution_sim:.3f}, "
				f"综合={sequence_similarity:.3f}"
			)

			return sequence_similarity

		except Exception as e:
			self.__logger.warning( f"点序列相似度计算失败: {str( e )}" )
			return 0.0

	def __calculate_point_count_matching( self, points1, points2 ) -> float:
		"""
		计算点数匹配相似度

		方法：min(count1, count2) / max(count1, count2)
		"""
		try:
			count1 = len( points1 )
			count2 = len( points2 )

			if count1 == 0 or count2 == 0:
				return 0.0

			# 点数相似度
			point_count_similarity = min( count1, count2 ) / max( count1, count2 )

			return float( point_count_similarity )

		except Exception:
			return 0.0

	def __calculate_distribution_pattern_similarity( self, points1, points2 ) -> float:
		"""
		计算分布模式相似度

		方法：计算相邻点角度变化的相关性
		"""
		try:
			import numpy as np

			# 计算角度序列
			angles1 = self.__calculate_angle_sequence( points1 )
			angles2 = self.__calculate_angle_sequence( points2 )

			if len( angles1 ) == 0 or len( angles2 ) == 0:
				return 0.5  # 默认中等相似度

			# 如果角度序列长度不同，使用简化的相似度计算
			if len( angles1 ) != len( angles2 ):
				return self.__calculate_angle_sequence_dtw_similarity( angles1, angles2 )

			# 角度序列长度相同，计算皮尔逊相关系数
			correlation = np.corrcoef( angles1, angles2 )[ 0, 1 ]

			if np.isnan( correlation ):
				return 0.5

			# 转换到[0,1]范围
			distribution_similarity = (correlation + 1) / 2

			return float( distribution_similarity )

		except Exception:
			return 0.5

	def __calculate_angle_sequence( self, points ) -> List[ float ]:
		"""
		计算角度序列

		方法：计算相邻边之间的角度变化
		"""
		try:
			import numpy as np

			if len( points ) < 3:
				return [ ]

			angles = [ ]
			points_np = np.array( points )

			for i in range( len( points_np ) ):
				# 获取三个连续的点
				p1 = points_np[ i ]
				p2 = points_np[ (i + 1) % len( points_np ) ]
				p3 = points_np[ (i + 2) % len( points_np ) ]

				# 计算两个向量
				v1 = p2 - p1
				v2 = p3 - p2

				# 计算角度
				dot_product = np.dot( v1, v2 )
				norms = np.linalg.norm( v1 ) * np.linalg.norm( v2 )

				if norms > 0:
					cos_angle = np.clip( dot_product / norms, -1.0, 1.0 )
					angle = np.arccos( cos_angle )
					angles.append( angle )

			return angles

		except Exception:
			return [ ]

	def __calculate_angle_sequence_dtw_similarity( self, angles1, angles2 ) -> float:
		"""
		计算角度序列的DTW相似度（简化版）

		方法：计算最小平均距离
		"""
		try:
			import numpy as np

			# 简化版DTW：计算最小平均距离
			min_distances = [ ]

			for angle1 in angles1:
				distances = [ abs( angle1 - angle2 ) for angle2 in angles2 ]
				min_distances.append( min( distances ) )

			for angle2 in angles2:
				distances = [ abs( angle2 - angle1 ) for angle1 in angles1 ]
				min_distances.append( min( distances ) )

			avg_distance = np.mean( min_distances )

			# 转换为相似度（距离越小，相似度越高）
			similarity = 1 / (1 + avg_distance)
			return float( similarity )

		except Exception:
			return 0.5

	def __calculate_shape_feature_similarity(
			self, points1: List[ List[ float ] ], points2: List[ List[ float ] ]
	) -> float:
		"""
		计算形状特征相似度

		特征指标：
		- 紧凑度：周长²/(4π×面积)，衡量规则程度
		- 长宽比：边界框的宽高比例
		- 凸性：实际面积/凸包面积，衡量复杂度
		- 填充率：实际面积/边界框面积，衡量占用度

		参数:
			points1: 第一个坐标集合
			points2: 第二个坐标集合

		返回:
			形状特征相似度 (0.0-1.0)
		"""
		try:
			from shapely.geometry import Polygon
			from shapely.errors import TopologicalError

			if not points1 or not points2:
				return 0.0

			# 创建shapely多边形对象
			poly1 = Polygon( points1 )
			poly2 = Polygon( points2 )

			# 确保多边形有效
			if not poly1.is_valid:
				poly1 = poly1.buffer( 0 )
			if not poly2.is_valid:
				poly2 = poly2.buffer( 0 )

			# 提取形状特征
			features1 = self.__extract_polygon_shape_features( poly1 )
			features2 = self.__extract_polygon_shape_features( poly2 )

			# 计算特征向量的余弦相似度
			feature_similarity = self.__calculate_feature_vector_similarity( features1, features2 )

			self.__logger.debug(
				f"形状特征相似度详情: 特征1={features1}, 特征2={features2}, 相似度={feature_similarity:.3f}"
			)

			return feature_similarity

		except (TopologicalError, Exception) as e:
			self.__logger.warning( f"形状特征相似度计算失败: {str( e )}" )
			return 0.0

	def __extract_polygon_shape_features( self, poly ):
		"""
		提取基于shapely多边形的形状特征

		返回:
			包含紧凑度、长宽比、凸性、填充率的特征向量
		"""
		try:
			import numpy as np

			# 基础几何属性
			area = poly.area
			perimeter = poly.length

			# 1. 紧凑度：周长²/(4π×面积)
			if area > 0:
				compactness = (perimeter ** 2) / (4 * np.pi * area)
			else:
				compactness = 0.0

			# 2. 长宽比：边界框的宽高比例
			bounds = poly.bounds  # (minx, miny, maxx, maxy)
			bbox_width = bounds[ 2 ] - bounds[ 0 ]
			bbox_height = bounds[ 3 ] - bounds[ 1 ]

			if bbox_width > 0 and bbox_height > 0:
				aspect_ratio = bbox_width / bbox_height
			else:
				aspect_ratio = 1.0

			# 3. 凸性：实际面积/凸包面积
			convex_hull = poly.convex_hull
			if convex_hull.area > 0:
				convexity = area / convex_hull.area
			else:
				convexity = 0.0

			# 4. 填充率：实际面积/边界框面积
			bbox_area = bbox_width * bbox_height
			if bbox_area > 0:
				fill_ratio = area / bbox_area
			else:
				fill_ratio = 0.0

			# 特征向量
			features = np.array(
				[
					compactness,  # 紧凑度
					aspect_ratio,  # 长宽比
					convexity,  # 凸性
					fill_ratio  # 填充率
				]
			)

			return features

		except Exception:
			import numpy as np
			return np.zeros( 4 )

	def __calculate_feature_vector_similarity( self, features1, features2 ) -> float:
		"""
		计算特征向量的相似度

		方法：使用余弦相似度
		"""
		try:
			import numpy as np

			# 计算余弦相似度
			dot_product = np.dot( features1, features2 )
			norm1 = np.linalg.norm( features1 )
			norm2 = np.linalg.norm( features2 )

			if norm1 == 0 or norm2 == 0:
				return 0.0

			cosine_similarity = dot_product / (norm1 * norm2)

			# 转换到[0,1]范围
			feature_similarity = (cosine_similarity + 1) / 2

			return float( feature_similarity )

		except Exception:
			return 0.0

	def __check_existing_record_by_image_id( self, table_name: str, image_id: str ) -> Optional[ Dict[ str, Any ] ]:
		"""
		根据 image_id 查询数据库中是否已存在相同记录

		参数:
			table_name: 表名
			image_id: 图像ID

		返回:
			如果存在返回记录字典，否则返回None
			记录包含: detection_id, obb_data, segmentation_data 等字段
		"""
		if not self.__db_client:
			self.__log_output.append( "数据库未连接", Colors.RED )
			self.__logger.error( "数据库未连接" )
			return None

		try:
			# 使用 PostgreSQLClient 的 fetch_data 方法
			condition_str = f"image_id == '{image_id}'"
			result = self.__db_client.fetch_data(
				table_name=table_name,
				condition_str=condition_str,
				columns=[ "detection_id", "obb_data", "segmentation_data", "model_output_type" ],
				limit=1
			)

			if result.get( "success", False ) and result.get( "data" ):
				# 找到了匹配的记录
				record = result[ "data" ][ 0 ]  # 取第一条记录
				return {
					"detection_id": record[ "detection_id" ],
					"obb_data": record.get( "obb_data" ),
					"segmentation_data": record.get( "segmentation_data" ),
					"model_output_type": record.get( "model_output_type" )
				}
			else:
				# 没有找到匹配的记录
				return None

		except Exception as e:
			self.__log_output.append( f"查询数据库时出错: {str( e )}", Colors.RED )
			self.__logger.error( f"查询数据库时出错: {str( e )}" )
			return None

	def __update_database_annotation(
			self, table_name: str, image_id: str, original_shapes: List[ Dict ], existing_record: Dict[ str, Any ],
			is_segment: bool
	) -> Dict[ str, Any ]:
		"""
		更新数据库中的标注数据（追加模式，不覆盖）

		参数:
			table_name: 表名
			image_id: 图像ID
			original_shapes: 原始标注数据
			existing_record: 现有数据库记录
			is_segment: 是否为分割数据

		返回:
			更新结果字典
		"""
		import json
		try:
			if not original_shapes:
				return {
					"success": False,
					"error": "原始数据无标注信息",
					"action": "skip"
				}

			# 获取现有的标注数据
			if is_segment:
				existing_annotations = existing_record.get( "segmentation_data", [ ] )
				field_name = "segmentation_data"
			else:
				existing_annotations = existing_record.get( "obb_data", [ ] )
				field_name = "obb_data"

			# 确保现有数据是列表格式
			if not isinstance( existing_annotations, list ):
				existing_annotations = [ ]

			# 合并标注数据：现有数据 + 符合30%严格阈值的新数据
			merged_annotations = existing_annotations.copy()
			added_count = 0

			# ==================== 新的多维度相似度判断逻辑 ====================
			# 根据摘要文档实现的严格相似度判断
			# 相似度≤30%时才追加标注数据
			#
			# 四个核心维度：
			# 1. 坐标位置相似度 (40%权重)
			# 2. 几何形状相似度 (35%权重)
			# 3. 形状特征相似度 (15%权重)
			# 4. 点序列相似度 (10%权重)
			#
			# 五层决策机制：
			# 1. 极高相似度检查 → 跳过
			# 2. 综合相似度≥70% → 跳过
			# 3. 双重验证≥60% → 跳过
			# 4. 单一维度≥70% → 跳过
			# 5. 综合相似度≤30% → 追加
			# ================================================================

			for orig_shape in original_shapes:
				orig_points = orig_shape.get( "points", [ ] )
				if not orig_points:
					continue

				# 初始化相似度跟踪变量
				max_similarity_score = 0.0  # 记录与现有标注的最大综合相似度
				best_similarity_info = None  # 记录最相似的标注的详细信息

				# 与所有现有标注进行多维度相似度比较
				for existing_shape in existing_annotations:
					existing_points = existing_shape.get( "points", [ ] )
					if not existing_points:
						continue

					# 核心相似度计算：四维度综合评估
					similarity_info = self.__calculate_comprehensive_similarity_and_decision(
						orig_points, existing_points
					)

					# 提取综合相似度分数进行比较
					current_similarity = similarity_info.get( 'comprehensive_similarity', 0.0 )

					# 记录最高相似度的标注信息（用于最终决策）
					if current_similarity > max_similarity_score:
						max_similarity_score = current_similarity
						best_similarity_info = similarity_info

				# ==================== 严格决策执行逻辑 ====================
				should_add = False  # 是否应该添加标注
				decision_reason = ""  # 决策原因说明

				if best_similarity_info:
					# 使用最相似标注的决策结果（30%严格阈值）
					if best_similarity_info[ 'decision' ] == 'add':
						should_add = True
						decision_reason = best_similarity_info[ 'reason' ]
					# 示例原因："相似度低，可以追加 (综合=0.234)"
					else:
						should_add = False
						decision_reason = best_similarity_info[ 'reason' ]
				# 示例原因："极高相似度 (坐标=0.987, 几何=0.965)"
				# 或："综合相似度过高 (0.823)"
				# 或："相似度超过30%阈值 (综合=0.456)"
				else:
					# 异常情况：没有获取到相似度信息（通常不会发生）
					should_add = True
					decision_reason = "无相似度信息，默认添加"

				# 执行最终决策
				if should_add:
					# 添加标注到合并列表
					merged_annotations.append( orig_shape )
					added_count += 1
					self.__logger.debug( f"追加标注: {decision_reason}" )
				else:
					# 跳过标注，记录跳过原因
					self.__logger.debug( f"跳过标注: {decision_reason}" )

			# 检查是否有数据需要追加
			if added_count == 0:
				# 没有追加任何数据，无需更新数据库
				self.__logger.info( f"无需更新数据库: {image_id} - 没有符合条件的标注需要追加" )
				return {
					"success": True,
					"action": "no_update_needed",
					"reason": "没有符合条件的数据需要追加",
					"field_name": field_name,
					"original_count": len( existing_annotations ),
					"added_count": 0,
					"final_count": len( existing_annotations ),
					"skipped_reason": "所有原始标注都不符合重叠率条件"
				}

			# 有数据需要追加，准备更新数据库
			# 确保数据以正确的格式传递给PostgreSQL客户端
			# 对于jsonb字段，需要确保数据被正确序列化
			try:
				# 将merged_annotations序列化为JSON字符串，然后再传递给数据库客户端
				# 这样可以确保PostgreSQL客户端正确识别为jsonb类型而不是数组类型
				serialized_annotations = json.dumps( merged_annotations, ensure_ascii=False )
				update_data = { field_name: serialized_annotations }
				self.__logger.info(
					f"准备更新数据库: {image_id} - {field_name}字段追加{added_count}个标注，数据已序列化为JSON字符串"
				)
			except (TypeError, ValueError) as e:
				self.__logger.error( f"序列化标注数据失败: {str( e )}" )
				return {
					"success": False,
					"error": f"序列化标注数据失败: {str( e )}",
					"action": "error"
				}

			# 执行数据库更新
			condition_str = f"image_id == '{image_id}'"
			result = self.__db_client.update_data(
				table_name=table_name,
				condition_str=condition_str,
				data_json=update_data
			)

			if result.get( "success", False ):
				updated_count = result.get( "updated_count", 0 )
				self.__logger.info( f"数据库更新成功: {image_id} - 更新了{updated_count}条记录" )
				return {
					"success": True,
					"updated_count": updated_count,
					"field_name": field_name,
					"original_count": len( existing_annotations ),
					"added_count": added_count,
					"final_count": len( merged_annotations ),
					"action": "updated"
				}
			else:
				error_msg = result.get( "error", "更新失败" )
				self.__logger.error( f"数据库更新失败: {image_id} - {error_msg}" )
				return {
					"success": False,
					"error": error_msg,
					"field_name": field_name,
					"original_count": len( existing_annotations ),
					"added_count": added_count,
					"action": "failed"
				}

		except Exception as e:
			return {
				"success": False,
				"error": f"更新数据库时出错: {str( e )}",
				"action": "error"
			}

	def __process_duplicate_check_and_update( self, table_name: str, item: Dict, is_segment: bool ) -> Dict[ str, Any ]:
		"""
		处理重复检查和更新逻辑

		参数:
			table_name: 表名
			item: 数据项
			is_segment: 是否为分割数据

		返回:
			处理结果字典
		"""
		# 获取 image_id
		data = item[ 'data' ]
		image_id = os.path.basename( data.get( 'imagePath', '' ) )

		if not image_id:
			return {
				"action": "insert",
				"reason": "无法获取image_id"
			}

		# 检查数据库中是否已存在该记录
		existing_record = self.__check_existing_record_by_image_id( table_name, image_id )

		if not existing_record:
			# 记录不存在，正常插入
			return {
				"action": "insert",
				"reason": "记录不存在"
			}

		# 记录存在，进行重叠率检查
		original_shapes = data.get( "shapes", [ ] )
		if not original_shapes:
			return {
				"action": "skip",
				"reason": "原始数据无标注信息"
			}

		# 获取数据库中的标注数据
		if is_segment:
			db_annotation_data = existing_record.get( "segmentation_data", [ ] )
		else:
			db_annotation_data = existing_record.get( "obb_data", [ ] )

		if not db_annotation_data:
			# 数据库中无标注数据，更新（此时existing_record中对应字段为空，直接添加新数据）
			update_result = self.__update_database_annotation(
				table_name, image_id, original_shapes, existing_record, is_segment
			)
			return {
				"action": "updated",
				"reason": "数据库中无标注数据",
				"update_result": update_result
			}

		# 检查 shapely 库是否可用
		if not self.__check_shapely_available():
			return {
				"action": "skip",
				"reason": "shapely库不可用，无法计算相似度"
			}

		# 直接进行数据更新（使用坐标和形状相似度判断）
		update_result = self.__update_database_annotation(
			table_name, image_id, original_shapes, existing_record, is_segment
		)

		return {
			"action": "updated",
			"reason": f"数据已更新: {update_result.get( 'reason', '' )}",
			"update_result": update_result
		}

	def __save_to_database(
			self, data_list: List[ Dict[ str, Any ] ], training_type: str,
			is_segment: bool, is_obb: bool, table_name: str,
			data_source: str, output_folder: str
	) -> int:
		"""
		将数据保存到数据库

		改进后的逻辑：
		1. 使用线程本地数据库连接，避免多线程冲突
		2. 增强错误处理和连接管理
		3. 确保连接的正确释放

		参数:
			data_list: 数据列表
			training_type: 训练类型("train", "val", "pretrain_train", "pretrain_val")
			is_segment: 是否为分割模型
			is_obb: 是否为OBB模型
			table_name: 表名
			data_source: 数据源
			output_folder: 图像输出文件夹

		返回:
			成功保存的记录数量
		"""
		# 使用主线程数据库连接（单线程版本）
		if not self.__db_client:
			self.__log_output.append( "数据库未连接", Colors.RED )
			self.__logger.error( "数据库未连接" )
			return 0

		if not data_list:
			return 0

		# UI显示简洁信息
		self.__log_output.append( f"处理 {training_type} 数据: {len( data_list )} 条", Colors.GREEN )
		self.__logger.info( f"开始处理 {training_type} 数据: {len( data_list )} 条" )

		success_count = 0
		batch_size = 100
		batches = [ data_list[ i:i + batch_size ] for i in range( 0, len( data_list ), batch_size ) ]

		for batch_idx, batch in enumerate( batches ):
			try:
				records_to_insert = [ ]

				for item in batch:
					data = item[ 'data' ]
					file_path = item[ 'file_path' ]

					# 新增：重复检查和更新逻辑
					duplicate_check_result = self.__process_duplicate_check_and_update( table_name, item, is_segment )

					# 更新进度显示（每处理一个数据项）
					self.__increment_progress()

					if duplicate_check_result[ "action" ] == "skip":
						# 跳过处理
						self.__logger.info(
							f"跳过处理: {os.path.basename( data.get( 'imagePath', '' ) )} - {duplicate_check_result[ 'reason' ]}"
						)
						continue
					elif duplicate_check_result[ "action" ] == "updated":
						# 已更新，记录详细日志
						update_result = duplicate_check_result.get( "update_result", { } )
						action = update_result.get( "action", "" )

						if update_result.get( "success", False ):
							if action == "no_update_needed":
								# 无需更新数据库的情况
								field_name = update_result.get( "field_name", "" )
								reason = update_result.get( "reason", "" )
								self.__log_output.append(
									f"无需更新: {os.path.basename( data.get( 'imagePath', '' ) )} - {reason}",
									Colors.ORANGE
								)
								self.__logger.info(
									f"无需更新数据库: {os.path.basename( data.get( 'imagePath', '' ) )} - {field_name}字段无符合条件的标注"
								)
							elif action == "updated":
								# 实际更新了数据库的情况
								added_count = update_result.get( "added_count", 0 )
								final_count = update_result.get( "final_count", 0 )
								field_name = update_result.get( "field_name", "" )
								self.__log_output.append(
									f"追加成功: {os.path.basename( data.get( 'imagePath', '' ) )} - 新增{added_count}个标注，总计{final_count}个",
									Colors.GREEN
								)
								self.__logger.info(
									f"追加标注数据: {os.path.basename( data.get( 'imagePath', '' ) )} - {field_name}字段新增{added_count}个标注，总计{final_count}个"
								)
								success_count += 1
						else:
							self.__log_output.append(
								f"追加失败: {os.path.basename( data.get( 'imagePath', '' ) )}", Colors.RED
							)
							self.__logger.error(
								f"追加失败: {os.path.basename( data.get( 'imagePath', '' ) )} - {update_result.get( 'error', '未知错误' )}"
							)
						continue
					# 如果 action == "insert"，继续正常的插入流程

					# 获取图像路径、宽度和高度
					image_path = self.__save_image( data, file_path, output_folder )
					if not image_path:
						continue

					image_width = data.get( 'imageWidth', 0 )
					image_height = data.get( 'imageHeight', 0 )
					image_id = os.path.basename( data.get( 'imagePath', '' ) )

					# 准备模型特定数据
					shapes = data.get( 'shapes', [ ] )
					segmentation_data = None
					obb_data = None

					if is_segment:
						segmentation_data = json.dumps( shapes )
					if is_obb:
						obb_data = json.dumps( shapes )

					# 确定训练类型（修复的标识逻辑）
					# 从item中获取标识字段，而不是从item['data']中获取
					training_type_json = { }

					# 检查并添加各种标识（从item中获取，不是从data中）
					if item.get( "pretrain_train" ) == "true":
						training_type_json[ "pretrain_train" ] = "true"
					if item.get( "pretrain_val" ) == "true":
						training_type_json[ "pretrain_val" ] = "true"
					if item.get( "train" ) == "true":
						training_type_json[ "train" ] = "true"
					if item.get( "val" ) == "true":
						training_type_json[ "val" ] = "true"

					# 如果没有任何标识，使用默认值
					if not training_type_json:
						training_type_json = { "type": "unknown" }
						self.__log_output.append( f"数据缺少标识字段，使用默认标识", Colors.ORANGE )
						self.__logger.warning( f"数据缺少标识字段，使用默认标识" )

					# 添加到记录列表
					model_output_type = "Seg" if is_segment else "OBB" if is_obb else "Detection"
					record = {
						"image_id": image_id,
						"image_path": image_path,
						"image_width": image_width,
						"image_height": image_height,
						"data_source": data_source,
						"segmentation_data": segmentation_data,
						"obb_data": obb_data,
						"model_output_type": model_output_type,
						"training_type": training_type_json,  # PostgreSQLClient会自动处理JSON序列化
						"file_hash": None,  # 可为null，暂不计算哈希值
						"inference_count": 0  # 初始推理次数为0
					}
					records_to_insert.append( record )

				# 批量插入数据
				if records_to_insert:
					try:
						# 使用主线程数据库连接进行插入
						result = self.__db_client.insert_data( table_name, records_to_insert )
						if result.get( "success", False ):
							success_count += len( records_to_insert )
							# 只在控制台记录详细的批次信息
							self.__logger.info( f"批次 {batch_idx + 1} 插入成功: {len( records_to_insert )} 条" )
						else:
							self.__log_output.append( f"批量插入失败: {result.get( 'error', '未知错误' )}", Colors.RED )
							self.__logger.error( f"批量插入失败: {result.get( 'error', '未知错误' )}" )
					except Exception as db_e:
						self.__log_output.append( f"数据库操作异常: {str( db_e )}", Colors.RED )
						self.__logger.error( f"数据库操作异常: {str( db_e )}" )
						self.__logger.error( traceback.format_exc() )

			except Exception as e:
				self.__log_output.append( f"处理批次 {batch_idx + 1}/{len( batches )} 时出错: {str( e )}", Colors.RED )
				self.__logger.error( f"处理批次 {batch_idx + 1}/{len( batches )} 时出错: {str( e )}" )
				self.__logger.error( traceback.format_exc() )

			# UI显示简洁进度，控制台记录详细信息
			self.__log_output.append( f"进度: {batch_idx + 1}/{len( batches )}, 成功: {success_count}", Colors.GREEN )
			self.__logger.info( f"已处理 {batch_idx + 1}/{len( batches )} 批数据，当前成功: {success_count}" )

		# UI显示简洁结果
		self.__log_output.append( f"{training_type} 数据处理完成: {success_count} 条", Colors.GREEN )
		self.__logger.info( f"完成 {training_type} 数据处理: 总计 {success_count} 条记录" )
		return success_count

	def __process_all_data( self, data_result: Dict[ str, Any ], params: Dict[ str, Any ] ) -> Dict[ str, int ]:
		"""
		处理所有数据并保存到数据库（单线程版本，无重复数据）

		参数:
			data_result: 包含all_data和statistics的数据结果
			params: 处理参数

		返回:
			处理结果统计
		"""
		all_data = data_result[ "all_data" ]
		statistics = data_result[ "statistics" ]

		# UI显示简洁信息
		self.__log_output.append( f"开始处理数据: {statistics[ 'total' ]} 条", Colors.GREEN )
		# 控制台记录详细信息
		self.__logger.info( "开始单线程处理数据（无重复保存）" )
		self.__logger.info( f"总数据量: {statistics[ 'total' ]} 条" )

		try:
			# 只处理一次数据列表，每条数据根据其标识保存
			count = self.__save_to_database(
				all_data,
				"mixed",  # 混合数据类型，通过标识区分
				params[ "is_segment" ],
				params[ "is_obb" ],
				params[ "table_name" ],
				params[ "data_source" ],
				params[ "output_folder" ]
			)

			# UI显示简洁结果
			self.__log_output.append( f"数据处理完成: {count} 条记录", Colors.GREEN )
			self.__logger.info( f"数据处理完成: 实际保存 {count} 条记录" )

			# 返回统计信息（用于显示，但实际只保存了count条）
			return {
				"processed": count,
				"statistics": statistics
			}

		except Exception as e:
			self.__log_output.append( f"处理数据时出错: {str( e )}", Colors.RED )
			self.__logger.error( f"处理数据时出错: {str( e )}" )
			self.__logger.error( traceback.format_exc() )
			return {
				"processed": 0,
				"statistics": statistics
			}

	def process_data( self ) -> Dict[ str, Any ]:
		"""
		处理数据的主函数，执行完整的数据处理流程

		返回:
			处理结果字典

		使用示例:
			manager = DataProcessingManager(line_edit_manager, checkbox_manager, label_manager, log_output, logger)
			result = manager.process_data()
			if result["success"]:
				print(f"成功处理了 {result['details']['train']} 条训练数据")
			else:
				print(f"处理失败: {result['message']}")
		"""
		# 获取UI参数
		params = self.__get_ui_parameters()

		# 验证必须的参数
		if not params[ "input_folder" ]:
			self.__log_output.append( "未指定输入文件夹", Colors.RED )
			self.__logger.error( "未指定输入文件夹" )
			return { "success": False, "message": "未指定输入文件夹" }

		if not params[ "output_folder" ]:
			self.__log_output.append( "未指定输出文件夹", Colors.RED )
			self.__logger.error( "未指定输出文件夹" )
			return { "success": False, "message": "未指定输出文件夹" }

		if not params[ "is_segment" ] and not params[ "is_obb" ]:
			self.__log_output.append( "未选择模型类型（Segment或OBB）", Colors.RED )
			self.__logger.error( "未选择模型类型（Segment或OBB）" )
			return { "success": False, "message": "未选择模型类型" }

		self.__log_output.append( "开始处理数据", Colors.GREEN )
		self.__logger.info( "开始处理数据" )

		# 连接数据库
		if not self.__connect_database( params[ "db_name" ] ):
			return { "success": False, "message": "数据库连接失败" }

		# 创建表
		if not self.__create_table( params[ "table_name" ] ):
			return { "success": False, "message": "创建表失败" }

		# 读取JSON文件
		json_data_list = self.__read_json_files( params[ "input_folder" ] )
		if not json_data_list:
			return { "success": False, "message": "未找到有效的JSON文件" }

		# 初始化进度显示，总进度为JSON文件数量
		self.__initialize_progress( len( json_data_list ) )

		# 过滤多标签数据(如果需要)
		if params[ "clean_multilabel" ]:
			json_data_list = self.__filter_multilabel_data( json_data_list, params[ "expected_unique_labels" ] )

		# 创建输出文件夹
		os.makedirs( params[ "output_folder" ], exist_ok=True )

		# 划分数据
		data_result = self.__split_data(
			json_data_list,
			params[ "train_ratio" ],
			params[ "pretrain_ratio" ]
		)

		# 处理并保存所有数据
		results = self.__process_all_data( data_result, params )

		# 获取实际处理的数量和统计信息
		actual_processed = results.get( "processed", 0 )
		statistics = results.get( "statistics", { } )

		# 完成进度显示
		self.__complete_progress()

		# UI显示简洁的最终结果
		self.__log_output.append( f"处理完成: {actual_processed} 条记录", Colors.GREEN )
		self.__logger.info( f"数据处理完成，实际保存 {actual_processed} 条记录（无重复）" )
		return {
			"success": True,
			"message": f"成功处理 {actual_processed} 条记录",
			"details": statistics  # 返回统计信息而不是重复的数据量
		}
