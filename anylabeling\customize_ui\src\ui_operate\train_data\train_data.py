import threading
import threading
import traceback
from typing import Any, Optional

from PyQt5.QtWidgets import QCheckBox
from global_tools.ui_tools import (
	LogOutput, LineEditManager, QPushButtonManager, SelectAllCheckBoxManager, Q<PERSON>abelManager,
	QProgressBarHelper, CheckBoxManager, get_widgets_from_layout,
)
from global_tools.utils import ClassInstanceManager
from global_tools.utils import Logger

from anylabeling.customize_ui.helper.const import X_LABEL_LOG
from anylabeling.customize_ui.helper.folder_importer import FolderImporter
from anylabeling.customize_ui.layout.layout import Ui_Form
from .enhanced_image import EnhancedImage
from .helper import ExclusiveControlManager, AnnotatedDataProcessor, AnnotationViewer, DatasetExporter,DirectoryCleaner
from .helper1 import JsonAnnotationClassIndexProcessor, TrainingDataExporter
from anylabeling.customize_ui.src.ui_operate.save_data.helper import DataProcessingManager


class Train:

	__enhanced_image: EnhancedImage

	def __init__( self, ui_form: Ui_Form, log_output: LogOutput, logger: Logger = None ):
		"""
		初始化Train类。

		Args:
			ui_form: UI表单对象
			log_output: 日志输出对象
			logger: 日志记录器对象，可选
		"""
		self.__ui_form = ui_form
		self.__logger: Logger = ClassInstanceManager.get_instance( X_LABEL_LOG )  # type: ignore
		self.__log_output = log_output
		self.__all_image_paths = [ ]
		self.__augmented_image_count = 0  # 初始化增强图像数量属性

		# --------------------- QLineEdit ---------------------
		self.__line_edit_manager = LineEditManager.get_instance()

		# --------------------- QPushButton ---------------------
		# self.__push_button_list = [
		# 	self.__ui_form.pushButton_31,
		# 	self.__ui_form.pushButton_20,
		# 	self.__ui_form.pushButton_22,
		# 	self.__ui_form.pushButton_24,
		# 	self.__ui_form.pushButton_17,
		# 	self.__ui_form.pushButton_16,
		# 	self.__ui_form.pushButton_15,
		# 	self.__ui_form.pushButton_18,
		# 	self.__ui_form.pushButton_19,
		# 	self.__ui_form.pushButton_36,
		# ]
		self.__push_button_manager = QPushButtonManager.get_instance()
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_31", slot=self.open_folder_image_button_function
		)
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_20", slot=self.__export_dataset_button_function
		)
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_22", slot=self.__get_annotation_count_button_function
		)
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_25", slot=self.__generate_yaml_config_button_function
		)
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_17", slot=self.__get_image_count_button_function
		)
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_16", slot=self.__get_augmented_image_count_button_function
		)
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_15", slot=self.__start_augmentation_button_function
		)
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_18", slot=self.__stop_augmentation_button_function
		)
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_19", slot=self.__start_split_dataset_button_function
		)
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_36", slot=self.__random_preview_augmented_image_button_function
		)

		# --------------------- QLabel ---------------------
		# self.__label_list = [
		# 	self.__ui_form.label_34,
		# 	self.__ui_form.label_37,
		# 	self.__ui_form.label_178,
		# 	self.__ui_form.label_69,
		# 	self.__ui_form.label_51,
		# 	self.__ui_form.label_12,
		# 	self.__ui_form.label_8,
		# 	self.__ui_form.label_15,
		# 	self.__ui_form.label_24,
		# 	self.__ui_form.label_27,
		# ]
		# self.__label_manager = QLabelManager( *self.__label_list )
		self.__label_manager = QLabelManager.get_instance()

		# --------------------- QCheckBox ---------------------

		# 管理多个复选框组的全选/取消全选功能。
		# 该管理器会自动在 `container` 列表中的每个指定布局（如 color, weather 等）内，
		# 动态添加一个文本为 "全选/取消全选" 的QCheckBox。
		# 点击该复选框，可以控制其所在布局内的所有其他兄弟QCheckBox同时被选中或取消选中。
		self.__select_all_checkbox_manager = SelectAllCheckBoxManager(
			container=[
				self.__ui_form.color,
				self.__ui_form.weather,
				self.__ui_form.artifact,
				self.__ui_form.geometric,
			],
			select_all_text="全选/取消全选",
			logger=self.__logger
		)

		self.__checkbox_manager = CheckBoxManager.get_instance()

		# 设置复选框为单选模式（互斥选择）- 静态方法版本
		# CheckBoxManager( *[ self.__ui_form.horizontalLayout_244, ], logger=self.__logger ).set_exclusive_selection() 
		CheckBoxManager.set_exclusive_selection_static(
			checkboxes=[ *get_widgets_from_layout( self.__ui_form.horizontalLayout_244, QCheckBox ) ],
			logger=self.__logger
		)
		CheckBoxManager.set_exclusive_selection_static(
			checkboxes=[ *get_widgets_from_layout( self.__ui_form.horizontalLayout_566, QCheckBox ) ],
			logger=self.__logger
		)

		# 实例化互斥逻辑管理器
		self.__exclusive_manager = ExclusiveControlManager(
			container=self.__ui_form.horizontalLayout_6, logger=self.__logger
		)

		# --------------------- QProgressBar ---------------------
		self.__progress_bar_list = [
			self.__ui_form.progressBar,
		]
		self.__progress_bar_helper = QProgressBarHelper( progress_bars=self.__progress_bar_list )

	def __call__( self, *args: Any, **kwds: Any ) -> Any:
		pass

	def ui_operate( self ):
		pass

	def open_folder_image_button_function( self ):
		# 打开文件夹图像按钮
		pass
		image_folder_path = self.__line_edit_manager.get_line_edit( "lineEdit_45" ).text()  # type: ignore

		try:

			def func():
				return [
					r"K:\PaddleDet\PaddleDetection-release-2.8\GUI\image\wow_screentshot\亚基矿\a497b6e8-a87c-4713-a794-0a06d6175778\00d98ec8-d485-4665-91e0-880908fd27d0.jpg",
					r"K:\PaddleDet\PaddleDetection-release-2.8\GUI\image\wow_screentshot\亚基矿\a497b6e8-a87c-4713-a794-0a06d6175778\0b0f63e4-e3d3-497a-a60f-701d8788e677.jpg",
				]

			FolderImporter.load_from_path( func )
		except Exception as e:
			traceback.print_exc()

	def __export_dataset_button_function( self ):
		# 导出标注数据集按钮

		# def func():
		# 	try:
		# 		logger = self.__logger

		# 		# 初始化标签显示
		# 		if self.__label_manager:
		# 			self.__label_manager.set_text( "label_34", "0/0" )
		# 			self.__label_manager.set_text( "label_37", "0/0" )

		# 		exporter = DatasetExporter(
		# 			self.__line_edit_manager, self.__checkbox_manager, self.__log_output, logger=logger,
		# 			label_manager=self.__label_manager
		# 		)
		# 		success = exporter.export_dataset()

		# 		if success:
		# 			self.__log_output.append( "数据集导出成功！", color="green" )
		# 		else:
		# 			self.__log_output.append( "数据集导出失败，请检查日志信息。", color="red" )
		# 	except Exception as e:
		# 		import traceback
		# 		self.__log_output.append( f"导出数据集时发生错误: {str( e )}", color="red" )
		# 		self.__log_output.append( traceback.format_exc(), color="red" )

		# th = threading.Thread( target=func )
		# th.start()

		def func():
			pass
			
			# ---------------------------------- 步骤 0 清理目录 ----------------------------------
			directory_cleaner = DirectoryCleaner(
				checkbox_manager=self.__checkbox_manager,
				lineedit_manager=self.__line_edit_manager,
				log_output=self.__log_output,
				logger=self.__logger
			)
			directory_cleaner.clean_directory_conditionally()

			# ---------------------------------- 步骤 1 补全json文件中的class索引 ----------------------------------
			json_annotation_class_index_processor = JsonAnnotationClassIndexProcessor(
				line_edit_manager=self.__line_edit_manager,
				log_output=self.__log_output,
				logger=self.__logger
			)
			json_annotation_class_index_processor.process_json_files()

			# ------------------------------- 步骤 2 将标注数据保存到数据库中 ----------------------------------------
			data_processing_manager = DataProcessingManager(
				line_edit_manager=self.__line_edit_manager,
				checkbox_manager=self.__checkbox_manager,
				label_manager=self.__label_manager,
				log_output=self.__log_output,
				logger=self.__logger
			)
			data_processing_manager.process_data()

			# ---------------------------------- 步骤 3 导出训练数据 -----------------------------------------------
			training_data_exporter = TrainingDataExporter(
				line_edit_manager=self.__line_edit_manager,
				checkbox_manager=self.__checkbox_manager,
				log_output=self.__log_output,
				logger=self.__logger,
				label_manager=self.__label_manager
			)
			training_data_exporter.export_training_data()

			# ---------------------------------- 步骤 4 导出标注的json文件 -----------------------------------------------
			exporter = DatasetExporter(
				self.__line_edit_manager,
				self.__checkbox_manager,
				self.__log_output,
				logger=self.__logger,
				label_manager=self.__label_manager
			)
			success = exporter.export_dataset()

		th = threading.Thread( target=func )
		th.start()

	def __get_annotation_count_button_function( self ):
		"""
		获取标注对象总数量的按钮函数（增强版本）。

		该方法直接调用AnnotationCounter类，支持高级功能如进度显示、
		错误重试、详细统计报告等。

		使用示例:
		```python
		# 在UI类中绑定按钮
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_22",
			slot=self.__get_annotation_count_button_function
		)
		```
		"""

		def func():
			try:
				from .helper import AnnotationCounter

				self.__log_output.append( "开始统计标注对象总数量...", color="blue" )

				# 直接创建增强型标注对象计数器实例
				counter = AnnotationCounter(
					line_edit_manager=self.__line_edit_manager,
					log_output=self.__log_output,
					logger=self.__logger,
					label_manager=self.__label_manager,
					batch_size=25,  # 适中的批量大小
					enable_progress=True,  # 启用进度显示
					max_retries=2,  # 合理的重试次数
					progress_update_interval=5  # 进度更新间隔
				)

				# 执行统计
				total_count = counter.count_annotations()

				# 获取详细统计报告
				report = counter.get_statistics_report()

				# 显示详细结果
				if total_count > 0:
					self.__log_output.append(
						f"统计完成！共找到 {total_count} 个标注对象，"
						f"处理成功率 {report[ 'success_rate' ]:.1f}%，"
						f"耗时 {report[ 'processing_time' ]:.2f}秒",
						color="green"
					)

					# 如果有失败的文件，显示警告
					if report[ 'failed_files' ] > 0:
						self.__log_output.append(
							f"注意：{report[ 'failed_files' ]} 个文件处理失败",
							color="yellow"
						)
				else:
					self.__log_output.append( "未找到有效的标注对象，请检查输入路径。", color="yellow" )

				# 记录操作完成日志
				if self.__logger:
					self.__logger.info(
						f"标注统计操作完成，共找到 {total_count} 个标注对象，"
						f"处理成功率 {report[ 'success_rate' ]:.1f}%"
					)

			except Exception as e:
				# 错误处理
				error_msg = f"执行标注统计时发生未知错误: {str( e )}"
				self.__log_output.append( error_msg, color="red" )
				self.__log_output.append( traceback.format_exc(), color="red" )
				if self.__logger:
					self.__logger.error( error_msg )
					self.__logger.error( traceback.format_exc() )

		# 在单独的线程中运行，避免阻塞UI
		th = threading.Thread( target=func, name="AnnotationCountThread" )
		th.daemon = True  # 设置为守护线程，主程序退出时自动结束
		th.start()

	def __get_image_count_button_function( self ):
		"""获取并统计匹配的图像与JSON文件对的数量"""
		from .helper import count_dataset_images

		def func():
			try:
				# 直接调用辅助函数执行统计和UI更新
				count_dataset_images(
					self.__line_edit_manager,
					self.__label_manager,
					self.__log_output,
					self.__logger,
				)
			except Exception as e:
				import traceback
				self.__log_output.append( f"执行图像统计时发生未知错误: {str( e )}", color="red" )
				self.__log_output.append( traceback.format_exc(), color="red" )

		# 在单独的线程中运行，避免阻塞UI
		th = threading.Thread( target=func )
		th.start()

	def __get_augmented_image_count_button_function( self ):
		"""获取生成增强图像数量"""
		from .helper import calculate_and_display_augmentation_count

		def func():
			try:
				# 调用辅助函数来执行计算和UI更新
				calculate_and_display_augmentation_count(
					line_edit_manager=self.__line_edit_manager,
					label_manager=self.__label_manager,
					exclusive_manager=self.__exclusive_manager,
					log_output=self.__log_output,
					logger=self.__logger,
				)
			except Exception as e:
				import traceback
				self.__log_output.append( f"执行增强数量计算时发生未知错误: {str( e )}", color="red" )
				self.__log_output.append( traceback.format_exc(), color="red" )

		# 在单独的线程中运行，避免阻塞UI
		th = threading.Thread( target=func )
		th.start()

	def __stop_augmentation_button_function( self ):
		"""停止增强图像按钮函数"""
		if self.__enhanced_image:
			self.__enhanced_image.stop_all()

	def __start_augmentation_button_function( self ):
		"""
		开始生成增强图像按钮函数（重构版本）。

		该方法现在通过实例化EnhancedImage类并调用其start_data_augmentation实例方法，
		将原有的复杂业务逻辑完全集成到了EnhancedImage类中，提高了代码的模块化和可维护性。

		使用示例:
		```python
		# 在UI中绑定按钮点击事件
		self.__push_button_manager.connect_clicked_signal(
			button_name="pushButton_15",
			slot=self.__start_augmentation_button_function
		)
		```
		"""
		import threading

		def func():
			try:
				# 创建EnhancedImage实例（所有参数在实例化时传递）
				self.__enhanced_image = EnhancedImage(
					line_edit_manager=self.__line_edit_manager,
					label_manager=self.__label_manager,
					exclusive_manager=self.__exclusive_manager,
					log_output=self.__log_output,
					logger=self.__logger,
					checkbox_manager=self.__checkbox_manager,
					push_button_manager=self.__push_button_manager,
					progress_bar_helper=self.__progress_bar_helper,
					ui_form=self.__ui_form
				)

				# 调用实例方法启动数据增强（无需传递参数）
				success = self.__enhanced_image.start_data_augmentation()

			except Exception as e:
				# 额外的错误处理层
				error_msg = f"执行数据增强启动时发生未知错误: {str( e )}"
				self.__log_output.append( error_msg, color="red" )
				if self.__logger:
					self.__logger.error( error_msg )
					self.__logger.error( traceback.format_exc() )

				# 重置相关属性
				self.__enhanced_image = None  # type: ignore
				self.__all_image_paths = [ ]
				self.__augmented_image_count = 0

		# 在单独的线程中运行，避免阻塞UI
		th = threading.Thread( target=func, name="DataAugmentationThread" )
		th.daemon = True  # 设置为守护线程，主程序退出时自动结束
		th.start()

	def __generate_yaml_config_button_function( self ):
		"""生成YAML配置文件按钮函数"""
		from .helper import YAMLConfigGenerator

		def func():
			try:
				self.__log_output.append( "开始生成YAML配置文件...", color="blue" )

				# 创建YAML配置生成器实例
				generator = YAMLConfigGenerator(
					self.__line_edit_manager,
					self.__log_output,
					self.__logger,  # type: ignore
					self.__label_manager
				)

				# 执行生成
				success = generator.generate_yaml_config()

				# 显示结果
				if success:
					self.__log_output.append( "YAML配置文件生成成功！", color="green" )
				else:
					self.__log_output.append( "YAML配置文件生成失败，请检查日志信息。", color="yellow" )

			except Exception as e:
				import traceback
				self.__log_output.append( f"生成YAML配置文件时发生错误: {str( e )}", color="red" )
				self.__log_output.append( traceback.format_exc(), color="red" )

		# 在单独的线程中运行，避免阻塞UI
		th = threading.Thread( target=func )
		th.start()

	def __start_split_dataset_button_function( self ):
		"""开始划分数据集按钮函数"""
		annotated_data_processor = AnnotatedDataProcessor(
			line_edit_manager=self.__line_edit_manager,
			checkbox_manager=self.__checkbox_manager,
			label_manager=self.__label_manager,
			log_output=self.__log_output,
			logger=self.__logger
		)

		def thread_func():
			annotated_data_processor.process_dataset()

		th = threading.Thread( target=thread_func )
		th.start()

	def __random_preview_augmented_image_button_function( self ):
		# 随机预览增强图像
		annotation_viewer = AnnotationViewer(
			line_edit_manager=self.__line_edit_manager, logger=self.__logger, log_output=self.__log_output
		)

		def thread_func():
			annotation_viewer.preview_annotations()

		th = threading.Thread( target=thread_func )
		th.start()
