#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于坐标相似度的标注比较处理器

严格按照 coordinate_similarity_processor_design.md 文档实现的多维度坐标相似度计算系统。
实现四个核心维度的相似度计算和五层智能决策机制，确保坐标相似度计算的合理性。

核心特性 (新架构):
前提条件检查:
- 尺度相似度计算 - 多尺度特征分析 (阈值≥40%)
- 形状相似度计算 - 基于Hausdorff距离的形状匹配 (阈值≥30%)

核心相似度计算:
1. 几何相似度计算 (权重50%) - 基于Shapely库的精确几何计算 (仅在前提条件满足时)
2. 位置相似度计算 (权重50%) - 多层次位置分析 (始终计算)

关键改进:
- 前提条件检查只影响几何相似度，位置相似度始终正常计算
- 综合相似度 = 几何相似度×50% + 位置相似度×50%
- 五层智能决策机制仅基于几何和位置相似度

作者: AI Assistant
创建时间: 2025-01-21
更新时间: 2025-07-21 (新架构)
版本: 2.0.0
"""

import os
import json
import time
import threading
from typing import Dict, List, Tuple, Any, Optional, Union
from pathlib import Path
import numpy as np

from global_tools.utils import Logger, Colors


class CoordinateSimilarityProcessor:
	"""
	基于坐标相似度的标注比较处理器 (新架构)

	新架构计算体系：
	前提条件检查：
	- 尺度相似度 - 面积比例、周长比例、边界框尺度、形状复杂度 (阈值≥40%)
	- 形状相似度 - Hausdorff距离、轮廓匹配、凸包相似度、描述符相似度 (阈值≥30%)

	核心相似度计算 (仅在前提条件满足时)：
	1. 几何相似度 (50%权重) - IoU、IoS、包含关系、几何中心距离
	2. 位置相似度 (50%权重) - 绝对位置、相对位置、边界框位置、质心偏移

	关键特性：
	- 前提条件检查只影响几何相似度计算，位置相似度始终正常计算
	- 尺度和形状相似度仅作为前提条件，不参与综合相似度计算
	- 综合相似度 = 几何相似度 × 50% + 位置相似度 × 50%

	五层智能决策机制：
	1. 快速排除检查 - 零坐标、极端尺寸差异、完全分离
	2. 高相似度检查 - 综合相似度≥0.85或几何/位置单维度极高
	3. 中等相似度细化判断 - 几何和位置维度一致性验证
	4. 低相似度确认 - 综合相似度≤0.30或关键维度否决
	5. 边界情况处理 - 模糊区间精细化判断

	使用示例:
		processor = CoordinateSimilarityProcessor(
			logger=logger,
			log_output=log_output,
			mode='strict'  # 严格模式
		)

		result = processor.compare_coordinates(
			points1=[[100,100], [200,100], [200,200], [100,200]],
			points2=[[105,105], [205,105], [205,205], [105,205]]
		)

		if result['decision'] == 'skip':
			print(f"坐标相似，跳过: {result['reason']}")
		else:
			print(f"坐标不同，追加: {result['reason']}")
	"""

	def __init__(
			self,
			logger: Optional[ Logger ] = None,
			log_output: Optional[ Any ] = None,
			mode: str = 'strict'  # 'strict' 或 'loose'
	):
		"""
		初始化坐标相似度处理器

		参数:
			logger: 日志记录器
			log_output: 日志输出对象
			mode: 计算模式，'strict'(严格模式)或'loose'(宽松模式)
		"""
		self.__logger = logger or Logger( "CoordinateSimilarityProcessor" )
		self.__log_output = log_output
		self.__mode = mode

		# 缓存管理
		self.__calculation_cache = { }  # 计算结果缓存
		self.__cache_lock = threading.Lock()  # 缓存锁
		self.__max_cache_size = 1000  # 最大缓存数量

		# 相似度权重配置 (新架构)
		# 注意：实际综合相似度计算只使用几何50% + 位置50%
		# 尺度和形状权重仅用于配置显示和兼容性，不参与实际计算
		self.__geometric_weight = 0.50  # 几何相似度权重50% (实际使用)
		self.__position_weight = 0.50   # 位置相似度权重50% (实际使用)
		self.__scale_weight = 0.00      # 尺度相似度权重0% (仅作前提条件)
		self.__shape_weight = 0.00      # 形状相似度权重0% (仅作前提条件)

		# 决策阈值配置 (根据模式设置)
		if mode == 'strict':
			# 严格模式阈值 (默认)
			self.__extreme_similarity_threshold = 0.90  # 极高相似度阈值
			self.__high_similarity_threshold = 0.75  # 高相似度阈值
			self.__medium_similarity_threshold = 0.60  # 中等相似度阈值
			self.__low_similarity_threshold = 0.40  # 低相似度阈值
		else:
			# 宽松模式阈值
			self.__extreme_similarity_threshold = 0.85
			self.__high_similarity_threshold = 0.70
			self.__medium_similarity_threshold = 0.55
			self.__low_similarity_threshold = 0.35

		# 细化判断阈值配置 (五层决策机制)
		self.__single_dimension_high_threshold = 0.95  # 单维度极高相似度阈值
		self.__multi_dimension_threshold = 0.75  # 多维度一致性阈值
		self.__key_dimension_geometric_threshold = 0.80  # 关键维度几何阈值
		self.__key_dimension_position_threshold = 0.75  # 关键维度位置阈值
		self.__geometric_veto_threshold = 0.30  # 几何维度否决阈值
		self.__position_veto_threshold = 0.20  # 位置维度否决阈值

		# 几何计算参数
		self.__min_area_threshold = 1e-6  # 最小面积阈值
		self.__max_size_ratio = 100.0  # 最大尺寸比例
		self.__distance_threshold_factor = 0.1  # 距离阈值因子
		self.__log_threshold = 2.0  # 对数阈值(允许4倍面积差异)

		# 前提条件阈值配置 (新增)
		self.__scale_prerequisite_threshold = 0.40  # 尺度相似度前提阈值 (40%)
		self.__shape_prerequisite_threshold = 0.30  # 形状相似度前提阈值 (30%)

		# 新架构决策阈值配置 (新增)
		self.__new_extreme_similarity_threshold = 0.85  # 新架构极高相似度阈值
		self.__new_low_similarity_threshold = 0.30      # 新架构低相似度阈值
		self.__new_high_similarity_threshold = 0.65     # 新架构接近高相似度阈值
		self.__new_single_dimension_high_threshold = 0.95  # 新架构单维度极高阈值
		self.__new_core_dimension_threshold = 0.70      # 新架构核心维度基本要求阈值
		self.__new_geometric_veto_threshold = 0.25      # 新架构几何维度否决阈值
		self.__new_position_veto_threshold = 0.20       # 新架构位置维度否决阈值
		self.__new_dimension_diff_threshold = 0.4       # 新架构维度差异阈值

		# 性能监控
		self.__calculation_count = 0
		self.__total_calculation_time = 0.0
		self.__performance_lock = threading.Lock()

		# 初始化L1快速筛选模块 (新架构)
		self.__initialize_quick_filter_module()

		# 初始化L2重叠度计算器 (新架构)
		self.__initialize_overlap_calculator()

		# 初始化L2空间位置计算器 (新架构)
		self.__initialize_spatial_calculator()

		# 初始化L2形态特征计算器 (新架构)
		self.__initialize_morphology_calculator()

		# 初始化L3自适应融合模块 (新架构)
		self.__initialize_adaptive_fusion()

		# 初始化新决策引擎 (新架构)
		self.__initialize_decision_engine()

		# 初始化多尺度分析器 (深度优化)
		self.__initialize_multiscale_analyzer()

		self.__logger.info( f"坐标相似度处理器初始化完成 - 模式: {mode}" )
		if self.__log_output:
			self.__log_output.append( f"坐标相似度处理器初始化完成 - 模式: {mode}", Colors.GREEN )

	def compare_coordinates(
			self,
			points1: List[ List[ float ] ],
			points2: List[ List[ float ] ],
			similarity_threshold: Optional[float] = 0.30
	) -> Dict[ str, Any ]:
		"""
		比较两个坐标集合的相似度 (新架构 - 向后兼容)

		新架构计算流程:
		L1: 快速筛选层 - 高效筛选明显不相似的坐标对
		L2: 精细计算层 - 三个独立模块(重叠度、空间位置、形态特征)
		L3: 自适应融合层 - 注意力机制动态权重分配
		决策引擎: 智能决策机制 (支持用户阈值和多层决策)

		降级机制:
		如果新架构任何一层计算失败，自动降级到多尺度分析器，确保系统稳定性

		参数:
			points1: 第一个坐标集合 [[x1,y1], [x2,y2], ...]
			points2: 第二个坐标集合 [[x1,y1], [x2,y2], ...]
			similarity_threshold: 相似度阈值，默认0.30
			                     - 数值 (0.0-1.0): 只有综合相似度低于此阈值才追加标注数据
			                     - None: 使用默认的智能决策，不应用用户阈值检查

		返回:
			包含相似度详情和决策结果的字典 (保持向后兼容格式)
			{
				'geometric_similarity': float,      # 重叠度相似度 (新架构L2)
				'position_similarity': float,       # 空间位置相似度 (新架构L2)
				'scale_similarity': float,          # 尺度相似度 (形态特征的一部分)
				'shape_similarity': float,          # 形状相似度 (形态特征的一部分)
				'comprehensive_similarity': float,  # 自适应融合相似度 (新架构L3)
				'decision': str,                    # 决策结果 ('add'/'skip')
				'reason': str,                      # 决策原因说明
				'processing_time': float,           # 处理时间(秒)
				'layer_decision': str,              # 决策层级说明
				'details': dict                     # 详细计算信息 (新架构扩展)
			}

		新架构扩展信息 (details字段):
			{
				'new_architecture': bool,           # 是否成功使用新架构
				'l2_calculations': dict,            # L2层各模块计算详情
				'l3_fusion': dict,                  # L3层融合详情
				'decision_engine': dict,            # 决策引擎详情
				'fallback_reason': str              # 降级原因(如果适用)
			}

		使用示例:
			# 基本使用 (与旧版本完全兼容)
			result = processor.compare_coordinates(
				points1=[[100,100], [200,100], [200,200], [100,200]],
				points2=[[105,105], [205,105], [205,205], [105,205]]
			)

			# 查看各维度相似度 (接口保持不变)
			print(f"几何相似度: {result['geometric_similarity']:.3f}")
			print(f"位置相似度: {result['position_similarity']:.3f}")
			print(f"综合相似度: {result['comprehensive_similarity']:.3f}")

			# 检查是否使用了新架构
			if result['details'].get('new_architecture', False):
				print("使用了新架构计算")
				fusion_weights = result['details']['l3_fusion']['fusion_weights']
				print(f"融合权重: {fusion_weights}")
			else:
				print(f"使用了降级方案: {result['details'].get('fallback_reason', '未知')}")
		"""
		# 参数验证 (在try块外，确保异常能正确抛出)
		if similarity_threshold is not None and not 0.0 <= similarity_threshold <= 1.0:
			raise ValueError( f"similarity_threshold必须在0.0-1.0范围内，当前值: {similarity_threshold}" )

		start_time = time.time()

		try:

			# 生成缓存键
			cache_key = self.__generate_cache_key( points1, points2, similarity_threshold )

			# 检查缓存
			with self.__cache_lock:
				if cache_key in self.__calculation_cache:
					cached_result = self.__calculation_cache[ cache_key ].copy()
					cached_result[ 'processing_time' ] = time.time() - start_time
					cached_result[ 'from_cache' ] = True
					return cached_result

			# L1: 快速筛选层 (新架构)
			quick_check_result = self.__quick_exclusion_check( points1, points2 )
			if quick_check_result:
				processing_time = time.time() - start_time
				result = self.__create_result(
					decision=quick_check_result[ 'decision' ],
					reason=quick_check_result[ 'reason' ],
					processing_time=processing_time,
					layer_decision=quick_check_result[ 'reason' ]
				)
				self.__cache_result( cache_key, result )
				return result

			# 坐标预处理和标准化
			processed_points1 = self.__preprocess_coordinates( points1 )
			processed_points2 = self.__preprocess_coordinates( points2 )

			if processed_points1 is None or processed_points2 is None:
				processing_time = time.time() - start_time
				result = self.__create_result(
					decision='add',
					reason='坐标预处理失败，默认追加',
					processing_time=processing_time,
					layer_decision='坐标预处理阶段'
				)
				return result

			# L2: 精细计算层 - 三个独立模块计算 (新架构)
			try:
				self.__logger.debug("开始L2精细计算层 - 三个独立模块计算")

				# L2.1: 重叠度相似度计算
				try:
					overlap_result = self.__overlap_calculator.calculate(processed_points1, processed_points2)
					self.__logger.debug(f"L2.1重叠度计算完成: overall={overlap_result.get('overall', 0.0):.3f}")
				except Exception as e:
					self.__logger.warning(f"L2.1重叠度计算失败: {str(e)}")
					raise

				# L2.2: 空间位置相似度计算
				try:
					spatial_result = self.__spatial_calculator.calculate(processed_points1, processed_points2)
					self.__logger.debug(f"L2.2空间位置计算完成: overall={spatial_result.get('overall', 0.0):.3f}")
				except Exception as e:
					self.__logger.warning(f"L2.2空间位置计算失败: {str(e)}")
					raise

				# L2.3: 形态特征相似度计算
				try:
					morphology_result = self.__morphology_calculator.calculate(processed_points1, processed_points2)
					self.__logger.debug(f"L2.3形态特征计算完成: overall={morphology_result.get('overall', 0.0):.3f}")
				except Exception as e:
					self.__logger.warning(f"L2.3形态特征计算失败: {str(e)}")
					raise

				# 验证L2计算结果的完整性
				if not all(key in result and 'overall' in result for key, result in
						  [('overlap', overlap_result), ('spatial', spatial_result), ('morphology', morphology_result)]):
					raise ValueError("L2计算器返回结果格式不完整")

				# 构建L2计算结果
				l2_similarities = {
					'overlap': overlap_result,
					'spatial': spatial_result,
					'morphology': morphology_result
				}

				self.__logger.debug("L2精细计算层完成，所有模块计算成功")

				# L3: 自适应融合层 - 注意力机制动态权重分配 (新架构)
				try:
					self.__logger.debug("开始L3自适应融合层 - 注意力机制动态权重分配")
					fusion_result = self.__adaptive_fusion.fuse(l2_similarities, processed_points1, processed_points2)

					# 验证融合结果的完整性
					required_keys = ['similarity', 'confidence', 'weights', 'components', 'fusion_reason']
					if not all(key in fusion_result for key in required_keys):
						raise ValueError(f"L3融合结果格式不完整，缺少必要字段: {required_keys}")

					# 验证融合相似度和置信度的有效性
					similarity = fusion_result['similarity']
					confidence = fusion_result['confidence']
					if not (0.0 <= similarity <= 1.0) or not (0.0 <= confidence <= 1.0):
						raise ValueError(f"L3融合结果数值无效: similarity={similarity}, confidence={confidence}")

					self.__logger.debug(f"L3自适应融合完成: similarity={similarity:.3f}, confidence={confidence:.3f}")
					self.__logger.debug(f"融合权重: {fusion_result['weights']}")

				except Exception as e:
					self.__logger.warning(f"L3自适应融合失败: {str(e)}")
					raise

				# 决策引擎: 智能决策机制 (新架构)
				try:
					self.__logger.debug("开始决策引擎 - 智能决策机制")
					decision_result = self.__decision_engine.decide(fusion_result, similarity_threshold)

					# 验证决策结果的完整性
					required_decision_keys = ['action', 'reason', 'confidence', 'details']
					if not all(key in decision_result for key in required_decision_keys):
						raise ValueError(f"决策引擎结果格式不完整，缺少必要字段: {required_decision_keys}")

					# 验证决策动作的有效性
					if decision_result['action'] not in ['add', 'skip']:
						raise ValueError(f"决策引擎返回无效动作: {decision_result['action']}")

					self.__logger.debug(f"决策引擎完成: action={decision_result['action']}, reason={decision_result['reason']}")

				except Exception as e:
					self.__logger.warning(f"决策引擎失败: {str(e)}")
					raise

				# 向后兼容性映射 - 将新架构结果映射到旧接口格式
				geometric_sim = overlap_result['overall']  # 重叠度 -> 几何相似度
				position_sim = spatial_result['overall']   # 空间位置 -> 位置相似度
				scale_sim = morphology_result['scale']     # 形态特征中的尺度相似度
				shape_sim = morphology_result['shape']     # 形态特征中的形状相似度
				comprehensive_sim = fusion_result['similarity']  # 自适应融合结果

				# 决策结果映射
				decision = decision_result['action']
				reason = decision_result['reason']
				layer_info = decision_result['details'].get('decision_layer', '新架构决策')

			except Exception as e:
				# L2/L3/决策引擎失败时的降级方案：使用多尺度分析器
				self.__logger.warning(f"新架构计算失败，降级到多尺度分析器: {str(e)}")

				multiscale_result = self.__multiscale_analyzer.analyze(processed_points1, processed_points2)

				# 降级映射
				geometric_sim = multiscale_result['local_features']['local_similarity']
				position_sim = multiscale_result['medium_features']['medium_similarity']
				scale_sim = multiscale_result['global_features']['shape_descriptor_similarity']
				shape_sim = multiscale_result['global_features']['geometric_invariant_similarity']
				comprehensive_sim = multiscale_result['fused_similarity']

				# 降级决策逻辑 (保持与新决策引擎一致的逻辑)
				# 优先检查用户阈值
				if similarity_threshold is not None and comprehensive_sim < similarity_threshold:
					decision = 'add'
					reason = f'降级决策：低于用户阈值({comprehensive_sim:.3f} < {similarity_threshold:.3f})，追加标注'
					layer_info = '降级用户阈值决策'
				elif comprehensive_sim > 0.75:
					decision = 'skip'
					reason = f'降级决策：多尺度相似度高({comprehensive_sim:.3f})，跳过标注'
					layer_info = '降级高相似度决策'
				elif comprehensive_sim < 0.25:
					decision = 'add'
					reason = f'降级决策：多尺度相似度低({comprehensive_sim:.3f})，追加标注'
					layer_info = '降级低相似度决策'
				else:
					decision = 'add'  # 降级时保守追加
					reason = f'降级决策：多尺度中等相似度({comprehensive_sim:.3f})，保守追加'
					layer_info = '降级中等相似度决策'

			processing_time = time.time() - start_time

			# 更新性能统计
			with self.__performance_lock:
				self.__calculation_count += 1
				self.__total_calculation_time += processing_time

			# 构建详细信息 - 根据是否使用新架构决定内容
			details = {
				'mode': self.__mode,
				'user_threshold': similarity_threshold,
				'decision_layer': layer_info
			}

			# 检查是否成功使用了新架构
			try:
				# 如果新架构变量存在，说明使用了新架构
				if 'l2_similarities' in locals() and 'fusion_result' in locals() and 'decision_result' in locals():
					details.update({
						'new_architecture': True,  # 标识使用新架构
						'architecture_version': '2.0.0',  # 新架构版本号
						'computation_flow': 'L1_QuickFilter -> L2_PreciseCalculation -> L3_AdaptiveFusion -> DecisionEngine',
						'l1_quick_filter': {  # L1层快速筛选详情
							'passed': True,  # 通过了快速筛选
							'filter_reason': '通过快速筛选，进入精细计算'
						},
						'l2_calculations': {  # L2层计算详情
							'overlap_similarity': l2_similarities['overlap'],
							'spatial_similarity': l2_similarities['spatial'],
							'morphology_similarity': l2_similarities['morphology'],
							'calculation_success': True,
							'individual_scores': {
								'overlap_overall': l2_similarities['overlap']['overall'],
								'spatial_overall': l2_similarities['spatial']['overall'],
								'morphology_overall': l2_similarities['morphology']['overall']
							}
						},
						'l3_fusion': {  # L3层融合详情
							'fusion_similarity': fusion_result['similarity'],
							'fusion_confidence': fusion_result['confidence'],
							'fusion_weights': fusion_result['weights'],
							'fusion_reason': fusion_result['fusion_reason'],
							'fusion_strategy': 'attention_mechanism',
							'components_used': list(fusion_result['weights'].keys())
						},
						'decision_engine': {  # 决策引擎详情
							'decision_confidence': decision_result['confidence'],
							'decision_details': decision_result['details'],
							'engine_version': 'simplified_three_layer',
							'user_threshold_applied': similarity_threshold is not None,
							'user_threshold_value': similarity_threshold
						},
						'performance_metrics': {  # 性能指标
							'total_processing_time': processing_time,
							'architecture_overhead': 'minimal',  # 新架构相对于降级方案的开销
							'cache_used': False
						}
					})
				else:
					# 使用了降级方案
					details.update({
						'new_architecture': False,  # 标识使用降级方案
						'architecture_version': '1.0.0',  # 降级架构版本号
						'computation_flow': 'L1_QuickFilter -> MultiscaleAnalyzer -> SimplifiedDecision',
						'fallback_reason': '新架构计算失败，使用多尺度分析器降级',
						'fallback_details': {
							'fallback_trigger': 'L2/L3/DecisionEngine_Exception',
							'fallback_strategy': 'multiscale_analyzer',
							'fallback_success': True
						},
						'multiscale_architecture': True,  # 降级到多尺度架构
						'multiscale_details': {
							'scale_confidence': multiscale_result.get('scale_confidence', 0.0),
							'local_features': multiscale_result.get('local_features', {}),
							'medium_features': multiscale_result.get('medium_features', {}),
							'global_features': multiscale_result.get('global_features', {}),
							'fused_similarity': multiscale_result.get('fused_similarity', 0.0)
						},
						'decision_logic': {
							'decision_type': 'simplified_threshold',
							'thresholds_used': {'high': 0.75, 'low': 0.25},
							'user_threshold_applied': similarity_threshold is not None,
							'user_threshold_value': similarity_threshold
						},
						'performance_metrics': {  # 性能指标
							'total_processing_time': processing_time,
							'fallback_overhead': 'acceptable',
							'cache_used': False
						}
					})
			except Exception:
				# 详细信息构建失败时的最小信息
				details.update({
					'new_architecture': False,
					'fallback_reason': '详细信息构建失败'
				})

			# 向后兼容性验证 - 确保所有相似度值在[0,1]范围内
			geometric_sim = max(0.0, min(1.0, geometric_sim))
			position_sim = max(0.0, min(1.0, position_sim))
			scale_sim = max(0.0, min(1.0, scale_sim))
			shape_sim = max(0.0, min(1.0, shape_sim))
			comprehensive_sim = max(0.0, min(1.0, comprehensive_sim))

			# 验证决策字段的有效性
			if decision not in ['add', 'skip']:
				self.__logger.warning(f"无效的决策值: {decision}，重置为'add'")
				decision = 'add'
				reason = f"决策值无效，重置为追加: {reason}"

			result = {
				'geometric_similarity': float(geometric_sim),
				'position_similarity': float(position_sim),
				'scale_similarity': float(scale_sim),
				'shape_similarity': float(shape_sim),
				'comprehensive_similarity': float(comprehensive_sim),
				'decision': decision,
				'reason': reason,
				'processing_time': float(processing_time),
				'layer_decision': layer_info,
				'details': details,
				'from_cache': False
			}

			# 记录计算结果日志 - 区分新架构和降级方案
			architecture_info = "新架构" if details.get('new_architecture', False) else "降级方案"
			self.__logger.debug(
				f"坐标相似度计算完成({architecture_info}): 几何={geometric_sim:.3f}, 位置={position_sim:.3f}, "
				f"尺度={scale_sim:.3f}, 形状={shape_sim:.3f}, 综合={comprehensive_sim:.3f}, "
				f"决策={decision}, 耗时={processing_time:.3f}s"
			)

			# 缓存结果
			self.__cache_result( cache_key, result )

			return result

		except Exception as e:
			processing_time = time.time() - start_time
			error_msg = f"坐标相似度计算失败: {str( e )}"
			self.__logger.error( error_msg )
			return self.__create_result(
				decision='add',
				reason=error_msg,
				processing_time=processing_time,
				layer_decision='异常处理'
			)

	def __generate_cache_key( self, points1: List[ List[ float ] ], points2: List[ List[ float ] ], similarity_threshold: Optional[float] ) -> str:
		"""生成缓存键"""
		import hashlib
		# 将坐标点排序后生成唯一键
		sorted_points1 = sorted( [ tuple( p ) for p in points1 ] )
		sorted_points2 = sorted( [ tuple( p ) for p in points2 ] )
		threshold_str = "None" if similarity_threshold is None else str(similarity_threshold)
		content = f"{sorted_points1}:{sorted_points2}:{self.__mode}:{threshold_str}"
		return hashlib.md5( content.encode() ).hexdigest()

	def __cache_result( self, cache_key: str, result: Dict[ str, Any ] ) -> None:
		"""缓存计算结果"""
		with self.__cache_lock:
			if len( self.__calculation_cache ) >= self.__max_cache_size:
				# 简单的LRU：删除第一个元素
				first_key = next( iter( self.__calculation_cache ) )
				del self.__calculation_cache[ first_key ]

			# 缓存时移除不需要缓存的字段
			cache_result = result.copy()
			cache_result.pop( 'processing_time', None )
			cache_result.pop( 'from_cache', None )
			self.__calculation_cache[ cache_key ] = cache_result

	def __create_result(
			self, decision: str, reason: str, processing_time: float = 0.0,
			layer_decision: str = "", **kwargs
	) -> Dict[ str, Any ]:
		"""创建标准化的结果字典"""
		result = {
			'geometric_similarity': 0.0,
			'position_similarity': 0.0,
			'scale_similarity': 0.0,
			'shape_similarity': 0.0,
			'comprehensive_similarity': 0.0,
			'decision': decision,
			'reason': reason,
			'processing_time': processing_time,
			'layer_decision': layer_decision,
			'details': { },
			'from_cache': False
		}
		result.update( kwargs )
		return result

	def __quick_exclusion_check(
			self, points1: List[ List[ float ] ],
			points2: List[ List[ float ] ]
	) -> Optional[ Dict[ str, str ] ]:
		"""
		L1: 快速筛选层 (新架构)

		优化的快速筛选逻辑：
		1. 基础验证 - 坐标有效性检查
		2. 几何特征提取 - O(1)时间复杂度的特征计算
		3. 多维度快速筛选 - 面积比、边界框重叠、中心距离、长宽比
		4. 详细筛选原因 - 提供可解释的筛选依据

		返回:
			如果需要快速排除，返回决策字典；否则返回None继续后续计算
		"""
		try:
			# 调用新的快速筛选模块
			should_proceed, filter_reason = self.__quick_filter_module.should_proceed(points1, points2)

			if not should_proceed:
				return {
					'decision': 'add',
					'reason': f'L1快速筛选: {filter_reason}'
				}

			# 通过快速筛选，继续后续计算
			return None

		except Exception as e:
			self.__logger.warning( f"快速筛选失败: {str( e )}" )
			return None

	def __initialize_quick_filter_module(self):
		"""初始化快速筛选模块"""
		filter_config = {
			'min_area_ratio': 0.01,  # 最小面积比1%
			'max_area_ratio': 100.0,  # 最大面积比100:1
			'max_center_distance_ratio': 3.0,  # 最大中心距离比
			'min_bbox_overlap': 0.0,  # 最小边界框重叠
			'min_points': 3,  # 最小点数
			'max_aspect_ratio_diff': 10.0  # 最大长宽比差异
		}
		self.__quick_filter_module = self.QuickFilterModule(filter_config)

	def __initialize_overlap_calculator(self):
		"""初始化重叠度计算器 (L2新架构)"""
		overlap_config = {
			'use_shapely': True,  # 优先使用Shapely
			'min_area_threshold': 1e-6,  # 最小面积阈值
			'iou_weight': 0.4,  # IoU权重40%
			'ios_weight': 0.3,  # IoS权重30%
			'containment_weight': 0.2,  # 包含关系权重20%
			'overlap_ratio_weight': 0.1  # 重叠面积比权重10%
		}
		self.__overlap_calculator = self.OverlapSimilarityCalculator(overlap_config, self.__logger)

	def __initialize_spatial_calculator(self):
		"""初始化空间位置计算器 (L2新架构)"""
		spatial_config = {
			'distance_factor': 0.1,  # 距离因子
			'max_distance_threshold': 100.0,  # 最大距离阈值
			'min_distance_threshold': 5.0,  # 最小距离阈值
			'absolute_weight': 0.4,  # 绝对位置权重40%
			'relative_weight': 0.3,  # 相对位置权重30%
			'directional_weight': 0.2,  # 方向相似度权重20%
			'scale_aware_weight': 0.1  # 尺度感知权重10%
		}
		self.__spatial_calculator = self.SpatialSimilarityCalculator(spatial_config, self.__logger)

	def __initialize_morphology_calculator(self):
		"""初始化形态特征计算器 (L2新架构)"""
		morphology_config = {
			'hausdorff_weight': 0.3,  # Hausdorff距离权重30%
			'scale_weight': 0.3,  # 尺度相似度权重30%
			'complexity_weight': 0.2,  # 复杂度相似度权重20%
			'symmetry_weight': 0.2,  # 对称性相似度权重20%
			'min_area_threshold': 1e-6,  # 最小面积阈值
			'log_threshold': 2.0  # 对数阈值
		}
		self.__morphology_calculator = self.MorphologySimilarityCalculator(morphology_config, self.__logger)

	def __initialize_adaptive_fusion(self):
		"""初始化自适应融合模块 (L3新架构)"""
		fusion_config = {
			'fusion_strategy': 'attention',  # 注意力机制融合
			'confidence_threshold': 0.8,  # 置信度阈值
			'min_weight': 0.1,  # 最小权重限制10%
			'max_weight': 0.8,  # 最大权重限制80%
			'stability_factor': 0.1  # 稳定性因子
		}
		self.__adaptive_fusion = self.AdaptiveFusionModule(fusion_config, self.__logger)

	def __initialize_decision_engine(self):
		"""初始化决策引擎 (新架构)"""
		decision_config = {
			'high_threshold': 0.75,  # 高相似度阈值75%
			'low_threshold': 0.25,  # 低相似度阈值25%
			'confidence_threshold': 0.6,  # 置信度阈值60%
			'medium_threshold_upper': 0.6,  # 中等相似度上限60%
			'medium_threshold_lower': 0.4  # 中等相似度下限40%
		}
		self.__decision_engine = self.DecisionEngine(decision_config, self.__logger)

	def __initialize_multiscale_analyzer(self):
		"""初始化多尺度分析器 (深度优化)"""
		multiscale_config = {
			'local_weight': 0.3,  # 局部特征权重30%
			'medium_weight': 0.4,  # 中等特征权重40%
			'global_weight': 0.3,  # 全局特征权重30%
			'detail_threshold': 5.0,  # 细节阈值
			'structure_threshold': 20.0  # 结构阈值
		}
		self.__multiscale_analyzer = self.MultiScaleAnalyzer(multiscale_config, self.__logger)

	class QuickFilterModule:
		"""
		L1: 快速筛选模块

		目标：在O(1)时间内快速排除明显不相似的坐标对
		特点：高效、准确、可解释
		"""

		def __init__(self, config: dict):
			# 筛选阈值配置
			self.min_area_ratio = config.get('min_area_ratio', 0.01)
			self.max_area_ratio = config.get('max_area_ratio', 100.0)
			self.max_center_distance_ratio = config.get('max_center_distance_ratio', 3.0)
			self.min_bbox_overlap = config.get('min_bbox_overlap', 0.0)
			self.min_points = config.get('min_points', 3)
			self.max_aspect_ratio_diff = config.get('max_aspect_ratio_diff', 10.0)

		def should_proceed(self, points1: List[List[float]], points2: List[List[float]]) -> tuple:
			"""
			快速筛选判断

			返回:
				(是否继续处理, 筛选原因)
			"""
			# 1. 基础验证
			validation_result = self._basic_validation(points1, points2)
			if not validation_result[0]:
				return validation_result

			# 2. 提取基础特征 (O(1)复杂度)
			try:
				features1 = self._extract_basic_features(points1)
				features2 = self._extract_basic_features(points2)
			except Exception as e:
				return False, f"特征提取失败: {str(e)}"

			# 3. 面积比例检查
			if features1['area'] > 0 and features2['area'] > 0:
				area_ratio = min(features1['area'], features2['area']) / max(features1['area'], features2['area'])
				if area_ratio < self.min_area_ratio:
					return False, f"面积差异过大: 比例={area_ratio:.4f} < {self.min_area_ratio}"

			# 4. 边界框重叠检查
			bbox_overlap = self._calculate_bbox_overlap(features1['bbox'], features2['bbox'])
			if bbox_overlap < self.min_bbox_overlap:
				return False, f"边界框无重叠: 重叠度={bbox_overlap:.3f}"

			# 5. 中心距离检查
			center_distance = self._calculate_center_distance(features1['center'], features2['center'])
			avg_diagonal = (features1['diagonal'] + features2['diagonal']) / 2
			if avg_diagonal > 0:
				distance_ratio = center_distance / avg_diagonal
				if distance_ratio > self.max_center_distance_ratio:
					return False, f"中心距离过远: 距离比例={distance_ratio:.3f} > {self.max_center_distance_ratio}"

			# 6. 长宽比差异检查
			aspect_ratio_diff = abs(features1['aspect_ratio'] - features2['aspect_ratio'])
			if aspect_ratio_diff > self.max_aspect_ratio_diff:
				return False, f"长宽比差异过大: 差异={aspect_ratio_diff:.3f} > {self.max_aspect_ratio_diff}"

			return True, "通过快速筛选"

		def _basic_validation(self, points1, points2) -> tuple:
			"""基础验证检查 (支持numpy数组)"""
			# 转换为numpy数组以统一处理
			try:
				import numpy as np
				if not isinstance(points1, np.ndarray):
					points1 = np.array(points1)
				if not isinstance(points2, np.ndarray):
					points2 = np.array(points2)
			except Exception:
				return False, "坐标转换失败"

			# 1. 空坐标检查
			if len(points1) == 0 or len(points2) == 0:
				return False, "存在空坐标"

			# 2. 点数检查
			if len(points1) < self.min_points or len(points2) < self.min_points:
				return False, f"坐标点不足{self.min_points}个"

			# 3. 格式检查
			try:
				# 检查维度
				if points1.ndim != 2 or points2.ndim != 2:
					return False, "坐标维度错误"
				if points1.shape[1] < 2 or points2.shape[1] < 2:
					return False, "坐标维度不足"

				# 检查数据类型
				if not np.issubdtype(points1.dtype, np.number) or not np.issubdtype(points2.dtype, np.number):
					return False, "坐标数据类型错误"

			except Exception:
				return False, "坐标格式验证失败"

			return True, "基础验证通过"

		def _extract_basic_features(self, points) -> dict:
			"""提取基础几何特征 (O(1)复杂度，支持numpy数组)"""
			try:
				import numpy as np
				if not isinstance(points, np.ndarray):
					points = np.array(points)

				# 计算边界框
				xs = points[:, 0]
				ys = points[:, 1]
				bbox = {
					'min_x': float(np.min(xs)), 'max_x': float(np.max(xs)),
					'min_y': float(np.min(ys)), 'max_y': float(np.max(ys))
				}

				# 计算基础特征
				width = bbox['max_x'] - bbox['min_x']
				height = bbox['max_y'] - bbox['min_y']
				area = self._calculate_polygon_area_fast(points)
				center = ((bbox['min_x'] + bbox['max_x']) / 2, (bbox['min_y'] + bbox['max_y']) / 2)
				diagonal = (width ** 2 + height ** 2) ** 0.5

				return {
					'bbox': bbox,
					'area': area,
					'center': center,
					'diagonal': diagonal,
					'width': width,
					'height': height,
					'aspect_ratio': width / height if height > 0 else float('inf')
				}
			except Exception:
				# 降级处理
				return {
					'bbox': {'min_x': 0, 'max_x': 0, 'min_y': 0, 'max_y': 0},
					'area': 0.0, 'center': (0, 0), 'diagonal': 0.0,
					'width': 0.0, 'height': 0.0, 'aspect_ratio': 1.0
				}

		def _calculate_polygon_area_fast(self, points) -> float:
			"""快速多边形面积计算 (鞋带公式，支持numpy数组)"""
			try:
				import numpy as np
				if not isinstance(points, np.ndarray):
					points = np.array(points)

				if len(points) < 3:
					return 0.0

				area = 0.0
				n = len(points)
				for i in range(n):
					j = (i + 1) % n
					area += points[i][0] * points[j][1]
					area -= points[j][0] * points[i][1]
				return abs(area) / 2.0
			except Exception:
				return 0.0

		def _calculate_bbox_overlap(self, bbox1: dict, bbox2: dict) -> float:
			"""计算边界框重叠度"""
			# 计算重叠区域
			overlap_left = max(bbox1['min_x'], bbox2['min_x'])
			overlap_right = min(bbox1['max_x'], bbox2['max_x'])
			overlap_top = max(bbox1['min_y'], bbox2['min_y'])
			overlap_bottom = min(bbox1['max_y'], bbox2['max_y'])

			# 检查是否有重叠
			if overlap_left >= overlap_right or overlap_top >= overlap_bottom:
				return 0.0

			# 计算重叠面积
			overlap_area = (overlap_right - overlap_left) * (overlap_bottom - overlap_top)

			# 计算边界框面积
			area1 = (bbox1['max_x'] - bbox1['min_x']) * (bbox1['max_y'] - bbox1['min_y'])
			area2 = (bbox2['max_x'] - bbox2['min_x']) * (bbox2['max_y'] - bbox2['min_y'])

			# 计算重叠比例 (相对于较小的边界框)
			min_area = min(area1, area2)
			return overlap_area / min_area if min_area > 0 else 0.0

		def _calculate_center_distance(self, center1: tuple, center2: tuple) -> float:
			"""计算中心距离"""
			return ((center1[0] - center2[0]) ** 2 + (center1[1] - center2[1]) ** 2) ** 0.5

	class OverlapSimilarityCalculator:
		"""
		L2: 重叠度相似度计算器 (新架构)

		专注：几何重叠度分析，不涉及位置和形态
		算法：IoU、IoS、包含关系、重叠面积比
		特点：独立计算、精确分析、可降级
		"""

		def __init__(self, config: dict, logger=None):
			self.use_shapely = config.get('use_shapely', True)
			self.min_area_threshold = config.get('min_area_threshold', 1e-6)
			self.iou_weight = config.get('iou_weight', 0.4)
			self.ios_weight = config.get('ios_weight', 0.3)
			self.containment_weight = config.get('containment_weight', 0.2)
			self.overlap_ratio_weight = config.get('overlap_ratio_weight', 0.1)
			self.logger = logger

		def calculate(self, points1: np.ndarray, points2: np.ndarray) -> dict:
			"""
			计算重叠度相似度

			返回:
				{
					'iou': float,           # IoU相似度
					'ios': float,           # IoS相似度
					'containment': float,   # 包含关系
					'overlap_ratio': float, # 重叠面积比
					'overall': float        # 综合重叠度
				}
			"""
			try:
				if self.use_shapely:
					return self._calculate_with_shapely(points1, points2)
				else:
					return self._calculate_fallback(points1, points2)
			except Exception as e:
				if self.logger:
					self.logger.warning(f"重叠度计算失败，使用降级方案: {str(e)}")
				# 降级到简单计算
				return self._calculate_fallback(points1, points2)

		def _calculate_with_shapely(self, points1: np.ndarray, points2: np.ndarray) -> dict:
			"""使用Shapely进行精确重叠度计算"""
			try:
				from shapely.geometry import Polygon
				from shapely.validation import make_valid

				# 创建和验证多边形
				poly1 = make_valid(Polygon(points1))
				poly2 = make_valid(Polygon(points2))

				# 如果修复后仍然无效，使用buffer方法
				if not poly1.is_valid:
					poly1 = poly1.buffer(0)
				if not poly2.is_valid:
					poly2 = poly2.buffer(0)

				# 计算面积
				area1 = poly1.area
				area2 = poly2.area

				if area1 < self.min_area_threshold or area2 < self.min_area_threshold:
					return self._zero_result()

				# 计算交集和并集
				intersection = poly1.intersection(poly2)
				union = poly1.union(poly2)

				intersection_area = intersection.area if hasattr(intersection, 'area') else 0.0
				union_area = union.area if hasattr(union, 'area') else (area1 + area2)

				# 计算各种重叠度指标
				iou = intersection_area / union_area if union_area > 0 else 0.0
				ios = intersection_area / min(area1, area2) if min(area1, area2) > 0 else 0.0

				# 包含关系分析
				containment = self._analyze_containment(area1, area2, intersection_area)

				# 重叠面积比
				overlap_ratio = intersection_area / max(area1, area2) if max(area1, area2) > 0 else 0.0

				# 综合重叠度 (权重可配置)
				overall = (self.iou_weight * iou +
						  self.ios_weight * ios +
						  self.containment_weight * containment +
						  self.overlap_ratio_weight * overlap_ratio)

				return {
					'iou': float(max(0.0, min(1.0, iou))),
					'ios': float(max(0.0, min(1.0, ios))),
					'containment': float(max(0.0, min(1.0, containment))),
					'overlap_ratio': float(max(0.0, min(1.0, overlap_ratio))),
					'overall': float(max(0.0, min(1.0, overall)))
				}

			except ImportError:
				if self.logger:
					self.logger.warning("Shapely库不可用，使用降级方案")
				return self._calculate_fallback(points1, points2)
			except Exception as e:
				if self.logger:
					self.logger.warning(f"Shapely计算失败: {str(e)}")
				return self._calculate_fallback(points1, points2)

		def _calculate_fallback(self, points1: np.ndarray, points2: np.ndarray) -> dict:
			"""降级方案：使用简单几何计算"""
			try:
				# 计算边界框
				bbox1 = self._calculate_bbox(points1)
				bbox2 = self._calculate_bbox(points2)

				# 计算面积
				area1 = self._calculate_polygon_area(points1)
				area2 = self._calculate_polygon_area(points2)

				if area1 < self.min_area_threshold or area2 < self.min_area_threshold:
					return self._zero_result()

				# 计算边界框重叠
				bbox_overlap_area = self._calculate_bbox_overlap_area(bbox1, bbox2)

				# 估算重叠度指标
				union_area = area1 + area2 - bbox_overlap_area
				iou = bbox_overlap_area / union_area if union_area > 0 else 0.0
				ios = bbox_overlap_area / min(area1, area2) if min(area1, area2) > 0 else 0.0

				# 简化的包含关系
				containment = self._analyze_containment(area1, area2, bbox_overlap_area)

				# 重叠面积比
				overlap_ratio = bbox_overlap_area / max(area1, area2) if max(area1, area2) > 0 else 0.0

				# 综合重叠度
				overall = (self.iou_weight * iou +
						  self.ios_weight * ios +
						  self.containment_weight * containment +
						  self.overlap_ratio_weight * overlap_ratio)

				return {
					'iou': float(max(0.0, min(1.0, iou))),
					'ios': float(max(0.0, min(1.0, ios))),
					'containment': float(max(0.0, min(1.0, containment))),
					'overlap_ratio': float(max(0.0, min(1.0, overlap_ratio))),
					'overall': float(max(0.0, min(1.0, overall)))
				}

			except Exception as e:
				if self.logger:
					self.logger.warning(f"降级计算失败: {str(e)}")
				return self._zero_result()

		def _analyze_containment(self, area1: float, area2: float, intersection_area: float) -> float:
			"""分析包含关系"""
			if intersection_area <= 0:
				return 0.0  # 完全分离

			# 计算包含比例
			contain_ratio1 = intersection_area / area1 if area1 > 0 else 0.0
			contain_ratio2 = intersection_area / area2 if area2 > 0 else 0.0
			max_contain_ratio = max(contain_ratio1, contain_ratio2)

			# 根据包含比例确定得分
			if max_contain_ratio >= 0.95:
				return 1.0  # 几乎完全包含
			elif max_contain_ratio >= 0.80:
				return 0.8  # 高度包含
			elif max_contain_ratio >= 0.60:
				return 0.6  # 中度包含
			elif max_contain_ratio >= 0.30:
				return 0.4  # 低度包含
			else:
				return 0.2  # 轻微重叠

		def _calculate_bbox(self, points: np.ndarray) -> dict:
			"""计算边界框"""
			xs = points[:, 0]
			ys = points[:, 1]
			return {
				'min_x': float(np.min(xs)), 'max_x': float(np.max(xs)),
				'min_y': float(np.min(ys)), 'max_y': float(np.max(ys))
			}

		def _calculate_polygon_area(self, points: np.ndarray) -> float:
			"""计算多边形面积 (鞋带公式)"""
			if len(points) < 3:
				return 0.0

			area = 0.0
			n = len(points)
			for i in range(n):
				j = (i + 1) % n
				area += points[i][0] * points[j][1]
				area -= points[j][0] * points[i][1]
			return abs(area) / 2.0

		def _calculate_bbox_overlap_area(self, bbox1: dict, bbox2: dict) -> float:
			"""计算边界框重叠面积"""
			# 计算重叠区域
			overlap_left = max(bbox1['min_x'], bbox2['min_x'])
			overlap_right = min(bbox1['max_x'], bbox2['max_x'])
			overlap_top = max(bbox1['min_y'], bbox2['min_y'])
			overlap_bottom = min(bbox1['max_y'], bbox2['max_y'])

			# 检查是否有重叠
			if overlap_left >= overlap_right or overlap_top >= overlap_bottom:
				return 0.0

			# 计算重叠面积
			return (overlap_right - overlap_left) * (overlap_bottom - overlap_top)

		def _zero_result(self) -> dict:
			"""返回零结果"""
			return {
				'iou': 0.0,
				'ios': 0.0,
				'containment': 0.0,
				'overlap_ratio': 0.0,
				'overall': 0.0
			}

	class SpatialSimilarityCalculator:
		"""
		L2: 空间位置相似度计算器 (新架构)

		专注：纯空间位置关系，不涉及重叠度和形态
		算法：多尺度位置分析、自适应距离阈值
		特点：独立计算、多维度分析、自适应阈值
		"""

		def __init__(self, config: dict, logger=None):
			self.distance_factor = config.get('distance_factor', 0.1)
			self.max_distance_threshold = config.get('max_distance_threshold', 100.0)
			self.min_distance_threshold = config.get('min_distance_threshold', 5.0)
			self.absolute_weight = config.get('absolute_weight', 0.4)
			self.relative_weight = config.get('relative_weight', 0.3)
			self.directional_weight = config.get('directional_weight', 0.2)
			self.scale_aware_weight = config.get('scale_aware_weight', 0.1)
			self.logger = logger

		def calculate(self, points1: np.ndarray, points2: np.ndarray) -> dict:
			"""
			计算空间位置相似度

			返回:
				{
					'absolute': float,      # 绝对位置相似度
					'relative': float,      # 相对位置相似度
					'directional': float,   # 方向相似度
					'scale_aware': float,   # 尺度感知位置相似度
					'overall': float        # 综合位置相似度
				}
			"""
			try:
				# 计算几何中心
				center1 = self._calculate_centroid(points1)
				center2 = self._calculate_centroid(points2)

				# 计算特征尺度
				scale1 = self._calculate_characteristic_scale(points1)
				scale2 = self._calculate_characteristic_scale(points2)
				avg_scale = (scale1 + scale2) / 2

				# 计算中心距离
				center_distance = np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)

				# 1. 绝对位置相似度 (调整阈值使其更敏感)
				distance_threshold = max(min(avg_scale * self.distance_factor, self.max_distance_threshold),
									   self.min_distance_threshold)
				# 使用更宽松的阈值，让相近位置有更高的相似度
				absolute_sim = np.exp(-center_distance / (distance_threshold * 2.0))

				# 2. 相对位置相似度 (归一化)
				normalized_distance = center_distance / avg_scale if avg_scale > 0 else 0
				relative_sim = np.exp(-normalized_distance * 1.5)  # 降低衰减速度

				# 3. 方向相似度 (基于主轴方向)
				directional_sim = self._calculate_directional_similarity(points1, points2)

				# 4. 尺度感知位置相似度 (修复计算逻辑)
				if max(scale1, scale2) > 0:
					scale_factor = min(scale1, scale2) / max(scale1, scale2)
					# 尺度感知相似度应该结合位置和尺度信息
					scale_aware_sim = absolute_sim * (0.5 + 0.5 * scale_factor)
				else:
					scale_aware_sim = absolute_sim * 0.5

				# 综合位置相似度
				overall = (self.absolute_weight * absolute_sim +
						  self.relative_weight * relative_sim +
						  self.directional_weight * directional_sim +
						  self.scale_aware_weight * scale_aware_sim)

				return {
					'absolute': float(max(0.0, min(1.0, absolute_sim))),
					'relative': float(max(0.0, min(1.0, relative_sim))),
					'directional': float(max(0.0, min(1.0, directional_sim))),
					'scale_aware': float(max(0.0, min(1.0, scale_aware_sim))),
					'overall': float(max(0.0, min(1.0, overall)))
				}

			except Exception as e:
				if self.logger:
					self.logger.warning(f"空间位置相似度计算失败: {str(e)}")
				return self._zero_result()

		def _calculate_centroid(self, points: np.ndarray) -> tuple:
			"""计算多边形质心"""
			if len(points) < 3:
				# 对于少于3个点的情况，返回平均坐标
				return (float(np.mean(points[:, 0])), float(np.mean(points[:, 1])))

			# 使用多边形质心公式
			area = 0.0
			cx = 0.0
			cy = 0.0
			n = len(points)

			for i in range(n):
				j = (i + 1) % n
				cross = points[i][0] * points[j][1] - points[j][0] * points[i][1]
				area += cross
				cx += (points[i][0] + points[j][0]) * cross
				cy += (points[i][1] + points[j][1]) * cross

			area = area / 2.0
			if abs(area) < 1e-10:
				# 面积为零时，返回平均坐标
				return (float(np.mean(points[:, 0])), float(np.mean(points[:, 1])))

			cx = cx / (6.0 * area)
			cy = cy / (6.0 * area)
			return (float(cx), float(cy))

		def _calculate_characteristic_scale(self, points: np.ndarray) -> float:
			"""计算特征尺度（边界框对角线长度）"""
			xs = points[:, 0]
			ys = points[:, 1]
			width = float(np.max(xs) - np.min(xs))
			height = float(np.max(ys) - np.min(ys))
			return (width ** 2 + height ** 2) ** 0.5

		def _calculate_directional_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算方向相似度（基于主轴方向）"""
			try:
				# 计算主轴方向
				angle1 = self._calculate_principal_axis_angle(points1)
				angle2 = self._calculate_principal_axis_angle(points2)

				# 计算角度差异
				angle_diff = abs(angle1 - angle2)
				# 处理角度周期性（0-180度）
				angle_diff = min(angle_diff, 180 - angle_diff)

				# 转换为相似度（角度差异越小，相似度越高）
				directional_sim = 1.0 - (angle_diff / 90.0)  # 归一化到0-1
				return max(0.0, min(1.0, directional_sim))

			except Exception:
				# 计算失败时返回中等相似度
				return 0.5

		def _calculate_principal_axis_angle(self, points: np.ndarray) -> float:
			"""计算主轴角度"""
			try:
				# 计算质心
				centroid = self._calculate_centroid(points)

				# 计算协方差矩阵
				centered_points = points - np.array(centroid)
				cov_matrix = np.cov(centered_points.T)

				# 计算特征值和特征向量
				eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

				# 找到最大特征值对应的特征向量（主轴方向）
				max_eigenvalue_index = np.argmax(eigenvalues)
				principal_axis = eigenvectors[:, max_eigenvalue_index]

				# 计算角度（弧度转角度）
				angle = np.arctan2(principal_axis[1], principal_axis[0]) * 180 / np.pi

				# 确保角度在0-180度范围内
				if angle < 0:
					angle += 180

				return float(angle)

			except Exception:
				# 计算失败时返回0度
				return 0.0

		def _zero_result(self) -> dict:
			"""返回零结果"""
			return {
				'absolute': 0.0,
				'relative': 0.0,
				'directional': 0.0,
				'scale_aware': 0.0,
				'overall': 0.0
			}

	class MorphologySimilarityCalculator:
		"""
		L2: 形态特征相似度计算器 (新架构)

		专注：形状和尺度特征，不涉及位置和重叠
		算法：形状描述符、尺度分析、复杂度评估
		特点：全面分析、高精度、可扩展
		"""

		def __init__(self, config: dict, logger=None):
			self.hausdorff_weight = config.get('hausdorff_weight', 0.3)
			self.scale_weight = config.get('scale_weight', 0.3)
			self.complexity_weight = config.get('complexity_weight', 0.2)
			self.symmetry_weight = config.get('symmetry_weight', 0.2)
			self.min_area_threshold = config.get('min_area_threshold', 1e-6)
			self.log_threshold = config.get('log_threshold', 2.0)
			self.logger = logger

		def calculate(self, points1: np.ndarray, points2: np.ndarray) -> dict:
			"""
			计算形态特征相似度

			返回:
				{
					'shape': float,         # 形状相似度
					'scale': float,         # 尺度相似度
					'complexity': float,    # 复杂度相似度
					'symmetry': float,      # 对称性相似度
					'overall': float        # 综合形态相似度
				}
			"""
			try:
				# 1. 形状相似度 (Hausdorff距离 + 轮廓匹配)
				shape_sim = self._calculate_shape_similarity(points1, points2)

				# 2. 尺度相似度 (面积、周长、边界框)
				scale_sim = self._calculate_scale_similarity(points1, points2)

				# 3. 复杂度相似度 (凸包比、边数、曲率)
				complexity_sim = self._calculate_complexity_similarity(points1, points2)

				# 4. 对称性相似度
				symmetry_sim = self._calculate_symmetry_similarity(points1, points2)

				# 综合形态相似度
				overall = (self.hausdorff_weight * shape_sim +
						  self.scale_weight * scale_sim +
						  self.complexity_weight * complexity_sim +
						  self.symmetry_weight * symmetry_sim)

				return {
					'shape': float(max(0.0, min(1.0, shape_sim))),
					'scale': float(max(0.0, min(1.0, scale_sim))),
					'complexity': float(max(0.0, min(1.0, complexity_sim))),
					'symmetry': float(max(0.0, min(1.0, symmetry_sim))),
					'overall': float(max(0.0, min(1.0, overall)))
				}

			except Exception as e:
				if self.logger:
					self.logger.warning(f"形态特征相似度计算失败: {str(e)}")
				return self._zero_result()

		def _calculate_shape_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算形状相似度（基于Hausdorff距离）"""
			try:
				# 使用简化的Hausdorff距离计算
				hausdorff_dist = self._calculate_hausdorff_distance(points1, points2)

				# 计算特征尺度用于归一化
				scale1 = self._calculate_characteristic_scale(points1)
				scale2 = self._calculate_characteristic_scale(points2)
				avg_scale = (scale1 + scale2) / 2

				if avg_scale > 0:
					# 归一化Hausdorff距离
					normalized_hausdorff = hausdorff_dist / avg_scale
					# 转换为相似度 (降低衰减速度，提高相似形状的得分)
					shape_similarity = np.exp(-normalized_hausdorff * 0.5)
				else:
					shape_similarity = 0.0

				return max(0.0, min(1.0, shape_similarity))

			except Exception:
				return 0.5  # 计算失败时返回中等相似度

		def _calculate_scale_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算尺度相似度（面积、周长、边界框）"""
			try:
				# 计算基本几何特征
				area1 = self._calculate_polygon_area(points1)
				area2 = self._calculate_polygon_area(points2)
				perimeter1 = self._calculate_polygon_perimeter(points1)
				perimeter2 = self._calculate_polygon_perimeter(points2)

				if area1 < self.min_area_threshold or area2 < self.min_area_threshold:
					return 0.0

				# 1. 面积比例相似度 (50%权重)
				area_ratio = max(area1, area2) / min(area1, area2)
				if area_ratio > 100:  # 极端比例
					area_similarity = 0.0
				else:
					area_similarity = 1.0 - min(1.0, abs(np.log(area_ratio)) / self.log_threshold)

				# 2. 周长比例相似度 (30%权重)
				if perimeter1 > 0 and perimeter2 > 0:
					perimeter_ratio = max(perimeter1, perimeter2) / min(perimeter1, perimeter2)
					if perimeter_ratio > 100:
						perimeter_similarity = 0.0
					else:
						perimeter_similarity = 1.0 - min(1.0, abs(np.log(perimeter_ratio)) / self.log_threshold)
				else:
					perimeter_similarity = 0.0

				# 3. 长宽比相似度 (20%权重)
				bbox1 = self._calculate_bbox(points1)
				bbox2 = self._calculate_bbox(points2)

				width1 = bbox1['max_x'] - bbox1['min_x']
				height1 = bbox1['max_y'] - bbox1['min_y']
				width2 = bbox2['max_x'] - bbox2['min_x']
				height2 = bbox2['max_y'] - bbox2['min_y']

				if width1 > 0 and height1 > 0 and width2 > 0 and height2 > 0:
					aspect_ratio1 = width1 / height1
					aspect_ratio2 = width2 / height2
					aspect_ratio_diff = abs(aspect_ratio1 - aspect_ratio2) / max(aspect_ratio1, aspect_ratio2)
					aspect_similarity = 1.0 - min(1.0, aspect_ratio_diff)
				else:
					aspect_similarity = 0.0

				# 综合尺度相似度
				scale_similarity = (0.5 * area_similarity +
								   0.3 * perimeter_similarity +
								   0.2 * aspect_similarity)

				return max(0.0, min(1.0, scale_similarity))

			except Exception:
				return 0.0

		def _calculate_complexity_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算复杂度相似度（凸包比、边数、曲率）"""
			try:
				# 1. 凸包复杂度 (50%权重)
				convex_complexity1 = self._calculate_convex_complexity(points1)
				convex_complexity2 = self._calculate_convex_complexity(points2)
				convex_diff = abs(convex_complexity1 - convex_complexity2)
				convex_similarity = 1.0 - min(1.0, convex_diff)

				# 2. 边数复杂度 (30%权重) - 提高敏感度
				edge_count1 = len(points1)
				edge_count2 = len(points2)
				edge_ratio = max(edge_count1, edge_count2) / min(edge_count1, edge_count2) if min(edge_count1, edge_count2) > 0 else 1.0
				# 边数差异越大，相似度越低
				edge_similarity = 1.0 / edge_ratio if edge_ratio > 0 else 0.0

				# 3. 形状紧凑度 (20%权重)
				compactness1 = self._calculate_compactness(points1)
				compactness2 = self._calculate_compactness(points2)
				compactness_diff = abs(compactness1 - compactness2)
				compactness_similarity = 1.0 - min(1.0, compactness_diff)

				# 综合复杂度相似度
				complexity_similarity = (0.5 * convex_similarity +
										0.3 * edge_similarity +
										0.2 * compactness_similarity)

				return max(0.0, min(1.0, complexity_similarity))

			except Exception:
				return 0.5

		def _calculate_symmetry_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算对称性相似度"""
			try:
				# 计算水平和垂直对称性
				h_symmetry1 = self._calculate_horizontal_symmetry(points1)
				h_symmetry2 = self._calculate_horizontal_symmetry(points2)
				v_symmetry1 = self._calculate_vertical_symmetry(points1)
				v_symmetry2 = self._calculate_vertical_symmetry(points2)

				# 对称性差异
				h_diff = abs(h_symmetry1 - h_symmetry2)
				v_diff = abs(v_symmetry1 - v_symmetry2)

				# 综合对称性相似度
				symmetry_similarity = 1.0 - (0.5 * h_diff + 0.5 * v_diff)

				return max(0.0, min(1.0, symmetry_similarity))

			except Exception:
				return 0.5

		def _calculate_hausdorff_distance(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算简化的Hausdorff距离"""
			try:
				# 计算从points1到points2的最大最小距离
				max_min_dist1 = 0.0
				for p1 in points1:
					min_dist = float('inf')
					for p2 in points2:
						dist = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
						min_dist = min(min_dist, dist)
					max_min_dist1 = max(max_min_dist1, min_dist)

				# 计算从points2到points1的最大最小距离
				max_min_dist2 = 0.0
				for p2 in points2:
					min_dist = float('inf')
					for p1 in points1:
						dist = np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
						min_dist = min(min_dist, dist)
					max_min_dist2 = max(max_min_dist2, min_dist)

				# Hausdorff距离是两个方向的最大值
				return max(max_min_dist1, max_min_dist2)

			except Exception:
				return float('inf')

		def _calculate_characteristic_scale(self, points: np.ndarray) -> float:
			"""计算特征尺度（边界框对角线长度）"""
			xs = points[:, 0]
			ys = points[:, 1]
			width = float(np.max(xs) - np.min(xs))
			height = float(np.max(ys) - np.min(ys))
			return (width ** 2 + height ** 2) ** 0.5

		def _calculate_polygon_area(self, points: np.ndarray) -> float:
			"""计算多边形面积 (鞋带公式)"""
			if len(points) < 3:
				return 0.0

			area = 0.0
			n = len(points)
			for i in range(n):
				j = (i + 1) % n
				area += points[i][0] * points[j][1]
				area -= points[j][0] * points[i][1]
			return abs(area) / 2.0

		def _calculate_polygon_perimeter(self, points: np.ndarray) -> float:
			"""计算多边形周长"""
			if len(points) < 2:
				return 0.0

			perimeter = 0.0
			n = len(points)
			for i in range(n):
				j = (i + 1) % n
				dist = np.sqrt((points[j][0] - points[i][0])**2 + (points[j][1] - points[i][1])**2)
				perimeter += dist
			return perimeter

		def _calculate_bbox(self, points: np.ndarray) -> dict:
			"""计算边界框"""
			xs = points[:, 0]
			ys = points[:, 1]
			return {
				'min_x': float(np.min(xs)), 'max_x': float(np.max(xs)),
				'min_y': float(np.min(ys)), 'max_y': float(np.max(ys))
			}

		def _calculate_convex_complexity(self, points: np.ndarray) -> float:
			"""计算凸包复杂度（多边形面积与凸包面积的比值）"""
			try:
				polygon_area = self._calculate_polygon_area(points)
				convex_area = self._calculate_convex_hull_area(points)

				if convex_area > 0:
					return polygon_area / convex_area
				else:
					return 0.0
			except Exception:
				return 0.0

		def _calculate_convex_hull_area(self, points: np.ndarray) -> float:
			"""计算凸包面积"""
			try:
				from scipy.spatial import ConvexHull
				if len(points) < 3:
					return 0.0
				hull = ConvexHull(points)
				return hull.volume  # 在2D中，volume就是面积
			except Exception:
				# 降级方案：使用边界框面积
				bbox = self._calculate_bbox(points)
				width = bbox['max_x'] - bbox['min_x']
				height = bbox['max_y'] - bbox['min_y']
				return width * height

		def _calculate_compactness(self, points: np.ndarray) -> float:
			"""计算形状紧凑度（4π×面积/周长²）"""
			try:
				area = self._calculate_polygon_area(points)
				perimeter = self._calculate_polygon_perimeter(points)

				if perimeter > 0:
					compactness = (4 * np.pi * area) / (perimeter ** 2)
					return min(1.0, compactness)  # 圆形的紧凑度为1
				else:
					return 0.0
			except Exception:
				return 0.0

		def _calculate_horizontal_symmetry(self, points: np.ndarray) -> float:
			"""计算水平对称性"""
			try:
				# 计算质心
				centroid_y = np.mean(points[:, 1])

				# 计算每个点关于水平轴的对称性
				symmetry_score = 0.0
				for point in points:
					# 找到关于水平轴对称的点
					symmetric_y = 2 * centroid_y - point[1]

					# 找到最近的实际点
					min_dist = float('inf')
					for other_point in points:
						dist = abs(other_point[1] - symmetric_y) + abs(other_point[0] - point[0])
						min_dist = min(min_dist, dist)

					# 计算对称性得分（距离越小，对称性越好）
					char_scale = self._calculate_characteristic_scale(points)
					if char_scale > 0:
						symmetry_score += np.exp(-min_dist / (char_scale * 0.1))

				return symmetry_score / len(points) if len(points) > 0 else 0.0

			except Exception:
				return 0.5

		def _calculate_vertical_symmetry(self, points: np.ndarray) -> float:
			"""计算垂直对称性"""
			try:
				# 计算质心
				centroid_x = np.mean(points[:, 0])

				# 计算每个点关于垂直轴的对称性
				symmetry_score = 0.0
				for point in points:
					# 找到关于垂直轴对称的点
					symmetric_x = 2 * centroid_x - point[0]

					# 找到最近的实际点
					min_dist = float('inf')
					for other_point in points:
						dist = abs(other_point[0] - symmetric_x) + abs(other_point[1] - point[1])
						min_dist = min(min_dist, dist)

					# 计算对称性得分
					char_scale = self._calculate_characteristic_scale(points)
					if char_scale > 0:
						symmetry_score += np.exp(-min_dist / (char_scale * 0.1))

				return symmetry_score / len(points) if len(points) > 0 else 0.0

			except Exception:
				return 0.5

		def _zero_result(self) -> dict:
			"""返回零结果"""
			return {
				'shape': 0.0,
				'scale': 0.0,
				'complexity': 0.0,
				'symmetry': 0.0,
				'overall': 0.0
			}

	class AdaptiveFusionModule:
		"""
		L3: 自适应融合层 (新架构)

		核心：注意力机制动态分配权重
		特点：可解释、自适应、稳定
		功能：替换固化的50%-50%权重分配
		"""

		def __init__(self, config: dict, logger=None):
			self.fusion_strategy = config.get('fusion_strategy', 'attention')  # 'attention', 'fixed', 'adaptive'
			self.confidence_threshold = config.get('confidence_threshold', 0.8)
			self.min_weight = config.get('min_weight', 0.1)  # 最小权重限制
			self.max_weight = config.get('max_weight', 0.8)  # 最大权重限制
			self.stability_factor = config.get('stability_factor', 0.1)  # 稳定性因子
			self.logger = logger

		def fuse(self, similarities: dict, points1: np.ndarray, points2: np.ndarray) -> dict:
			"""
			自适应融合相似度

			参数:
				similarities: {
					'overlap': dict,     # 重叠度相似度结果
					'spatial': dict,     # 空间位置相似度结果
					'morphology': dict   # 形态特征相似度结果
				}

			返回:
				{
					'similarity': float,        # 融合后的综合相似度
					'confidence': float,        # 融合置信度
					'weights': dict,           # 各维度权重
					'components': dict,        # 各维度详细结果
					'fusion_reason': str       # 融合策略说明
				}
			"""
			try:
				# 计算注意力权重
				attention_weights = self._calculate_attention_weights(similarities, points1, points2)

				# 执行加权融合
				fused_similarity = sum(
					attention_weights[key] * similarities[key]['overall']
					for key in similarities.keys()
				)

				# 计算融合置信度
				confidence = self._calculate_fusion_confidence(similarities, attention_weights)

				# 生成融合说明
				fusion_reason = self._generate_fusion_reason(attention_weights, similarities)

				return {
					'similarity': float(max(0.0, min(1.0, fused_similarity))),
					'confidence': float(max(0.0, min(1.0, confidence))),
					'weights': attention_weights,
					'components': similarities,
					'fusion_reason': fusion_reason
				}

			except Exception as e:
				if self.logger:
					self.logger.warning(f"自适应融合失败: {str(e)}")
				# 标记为降级融合
				fallback_result = self._fallback_fusion(similarities)
				fallback_result['fusion_reason'] = f"降级融合: {fallback_result['fusion_reason']}"
				return fallback_result

		def _calculate_attention_weights(self, similarities: dict, points1: np.ndarray, points2: np.ndarray) -> dict:
			"""计算注意力权重（基于几何特征的自适应权重）"""
			try:
				# 分析几何特征
				geometry_analysis = self._analyze_geometry_characteristics(points1, points2)

				# 基于几何特征的自适应权重
				if geometry_analysis['area_ratio'] < 0.3:
					# 尺寸差异大，更关注形态特征
					base_weights = {'overlap': 0.2, 'spatial': 0.3, 'morphology': 0.5}
					reason = "尺寸差异大，重点关注形态特征"
				elif geometry_analysis['shape_complexity'] > 0.8:
					# 形状复杂，更关注重叠度
					base_weights = {'overlap': 0.5, 'spatial': 0.3, 'morphology': 0.2}
					reason = "形状复杂，重点关注重叠度"
				elif geometry_analysis['center_distance_ratio'] > 2.0:
					# 距离较远，更关注空间位置
					base_weights = {'overlap': 0.3, 'spatial': 0.5, 'morphology': 0.2}
					reason = "距离较远，重点关注空间位置"
				else:
					# 一般情况，均衡权重
					base_weights = {'overlap': 0.4, 'spatial': 0.4, 'morphology': 0.2}
					reason = "一般情况，均衡权重分配"

				# 基于相似度置信度的权重调整
				adjusted_weights = self._adjust_weights_by_confidence(base_weights, similarities)

				# 应用权重约束
				final_weights = self._apply_weight_constraints(adjusted_weights)

				# 记录权重决策原因
				if hasattr(self, '_weight_decision_reason'):
					self._weight_decision_reason = reason

				return final_weights

			except Exception as e:
				if self.logger:
					self.logger.warning(f"注意力权重计算失败: {str(e)}")
				# 降级到均衡权重
				return {'overlap': 0.4, 'spatial': 0.4, 'morphology': 0.2}

		def _analyze_geometry_characteristics(self, points1: np.ndarray, points2: np.ndarray) -> dict:
			"""分析几何特征"""
			try:
				# 计算面积
				area1 = self._calculate_polygon_area(points1)
				area2 = self._calculate_polygon_area(points2)
				area_ratio = min(area1, area2) / max(area1, area2) if max(area1, area2) > 0 else 0.0

				# 计算形状复杂度（边数归一化，更敏感的阈值）
				complexity1 = min(len(points1) / 6.0, 1.0)  # 降低阈值，6个点以上认为复杂
				complexity2 = min(len(points2) / 6.0, 1.0)
				shape_complexity = max(complexity1, complexity2)  # 使用最大值而不是平均值

				# 计算中心距离比
				center1 = (np.mean(points1[:, 0]), np.mean(points1[:, 1]))
				center2 = (np.mean(points2[:, 0]), np.mean(points2[:, 1]))
				center_distance = np.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)

				# 计算特征尺度
				scale1 = self._calculate_characteristic_scale(points1)
				scale2 = self._calculate_characteristic_scale(points2)
				avg_scale = (scale1 + scale2) / 2
				center_distance_ratio = center_distance / avg_scale if avg_scale > 0 else 0.0

				return {
					'area_ratio': area_ratio,
					'shape_complexity': shape_complexity,
					'center_distance_ratio': center_distance_ratio,
					'avg_scale': avg_scale
				}

			except Exception:
				# 返回默认值
				return {
					'area_ratio': 0.5,
					'shape_complexity': 0.5,
					'center_distance_ratio': 1.0,
					'avg_scale': 100.0
				}

		def _adjust_weights_by_confidence(self, base_weights: dict, similarities: dict) -> dict:
			"""基于相似度置信度调整权重"""
			try:
				adjusted_weights = base_weights.copy()

				# 计算各维度的置信度（基于相似度的一致性）
				for key in similarities.keys():
					if key in adjusted_weights:
						sim_values = list(similarities[key].values())
						if len(sim_values) > 1:
							# 计算相似度值的方差作为置信度指标
							variance = np.var(sim_values)
							confidence = np.exp(-variance * 5.0)  # 方差越小，置信度越高

							# 根据置信度调整权重
							if confidence > 0.8:
								adjusted_weights[key] *= 1.1  # 高置信度增加权重
							elif confidence < 0.3:
								adjusted_weights[key] *= 0.9  # 低置信度减少权重

				# 归一化权重
				total_weight = sum(adjusted_weights.values())
				if total_weight > 0:
					for key in adjusted_weights:
						adjusted_weights[key] /= total_weight

				return adjusted_weights

			except Exception:
				return base_weights

		def _apply_weight_constraints(self, weights: dict) -> dict:
			"""应用权重约束（防止极端权重分配）"""
			constrained_weights = {}

			for key, weight in weights.items():
				# 应用最小和最大权重限制
				constrained_weights[key] = max(self.min_weight, min(self.max_weight, weight))

			# 重新归一化
			total_weight = sum(constrained_weights.values())
			if total_weight > 0:
				for key in constrained_weights:
					constrained_weights[key] /= total_weight

			return constrained_weights

		def _calculate_fusion_confidence(self, similarities: dict, weights: dict) -> float:
			"""计算融合置信度"""
			try:
				# 基于各维度相似度的一致性计算置信度
				overall_similarities = [similarities[key]['overall'] for key in similarities.keys()]

				if len(overall_similarities) < 2:
					return 0.5

				# 计算相似度的标准差
				std_dev = np.std(overall_similarities)

				# 标准差越小，置信度越高
				consistency_confidence = np.exp(-std_dev * 3.0)

				# 基于权重分布的置信度
				weight_values = list(weights.values())
				weight_entropy = -sum(w * np.log(w + 1e-10) for w in weight_values)
				max_entropy = np.log(len(weight_values))
				weight_confidence = 1.0 - (weight_entropy / max_entropy) if max_entropy > 0 else 0.5

				# 综合置信度
				overall_confidence = 0.7 * consistency_confidence + 0.3 * weight_confidence

				return max(0.0, min(1.0, overall_confidence))

			except Exception:
				return 0.5

		def _generate_fusion_reason(self, weights: dict, similarities: dict) -> str:
			"""生成融合策略说明"""
			try:
				# 找到权重最高的维度
				max_weight_key = max(weights.keys(), key=lambda k: weights[k])
				max_weight = weights[max_weight_key]

				# 生成说明
				dimension_names = {
					'overlap': '重叠度',
					'spatial': '空间位置',
					'morphology': '形态特征'
				}

				if max_weight > 0.5:
					reason = f"主要基于{dimension_names.get(max_weight_key, max_weight_key)}相似度"
				else:
					reason = "均衡融合多维度相似度"

				# 添加权重分布信息
				weight_info = ", ".join([f"{dimension_names.get(k, k)}:{v:.1%}" for k, v in weights.items()])
				reason += f" (权重分布: {weight_info})"

				return reason

			except Exception:
				return "标准融合策略"

		def _fallback_fusion(self, similarities: dict) -> dict:
			"""降级融合方案"""
			try:
				# 使用简单平均
				overall_similarities = [similarities[key]['overall'] for key in similarities.keys()]
				avg_similarity = sum(overall_similarities) / len(overall_similarities) if overall_similarities else 0.0

				# 均匀权重
				uniform_weights = {key: 1.0/len(similarities) for key in similarities.keys()}

				return {
					'similarity': float(max(0.0, min(1.0, avg_similarity))),
					'confidence': 0.3,  # 降级方案置信度较低
					'weights': uniform_weights,
					'components': similarities,
					'fusion_reason': '降级融合：简单平均'
				}

			except Exception:
				return {
					'similarity': 0.0,
					'confidence': 0.0,
					'weights': {},
					'components': {},
					'fusion_reason': '融合失败'
				}

		def _calculate_polygon_area(self, points: np.ndarray) -> float:
			"""计算多边形面积 (鞋带公式)"""
			if len(points) < 3:
				return 0.0

			area = 0.0
			n = len(points)
			for i in range(n):
				j = (i + 1) % n
				area += points[i][0] * points[j][1]
				area -= points[j][0] * points[i][1]
			return abs(area) / 2.0

		def _calculate_characteristic_scale(self, points: np.ndarray) -> float:
			"""计算特征尺度（边界框对角线长度）"""
			xs = points[:, 0]
			ys = points[:, 1]
			width = float(np.max(xs) - np.min(xs))
			height = float(np.max(ys) - np.min(ys))
			return (width ** 2 + height ** 2) ** 0.5

	class MultiScaleAnalyzer:
		"""
		多尺度分析器 (深度优化)

		核心：多尺度特征提取和融合
		特点：局部、中等、全局三个尺度的综合分析
		目标：提升算法精度15%，增强细节识别能力
		"""

		def __init__(self, config: dict, logger=None):
			self.local_weight = config.get('local_weight', 0.3)
			self.medium_weight = config.get('medium_weight', 0.4)
			self.global_weight = config.get('global_weight', 0.3)
			self.detail_threshold = config.get('detail_threshold', 5.0)
			self.structure_threshold = config.get('structure_threshold', 20.0)
			self.logger = logger

		def analyze(self, points1: np.ndarray, points2: np.ndarray) -> dict:
			"""
			多尺度分析主入口

			返回:
				{
					'local_features': dict,     # 局部尺度特征
					'medium_features': dict,    # 中等尺度特征
					'global_features': dict,    # 全局尺度特征
					'fused_similarity': float,  # 融合相似度
					'scale_confidence': float   # 尺度置信度
				}
			"""
			try:
				# 计算特征尺度
				scale1 = self._calculate_characteristic_scale(points1)
				scale2 = self._calculate_characteristic_scale(points2)
				avg_scale = (scale1 + scale2) / 2

				# 局部尺度分析 (细节特征)
				local_features = self._extract_local_features(points1, points2, avg_scale)

				# 中等尺度分析 (结构特征)
				medium_features = self._extract_medium_features(points1, points2, avg_scale)

				# 全局尺度分析 (整体特征)
				global_features = self._extract_global_features(points1, points2, avg_scale)

				# 多尺度融合
				fused_similarity, scale_confidence = self._fuse_multi_scale(
					local_features, medium_features, global_features
				)

				return {
					'local_features': local_features,
					'medium_features': medium_features,
					'global_features': global_features,
					'fused_similarity': float(max(0.0, min(1.0, fused_similarity))),
					'scale_confidence': float(max(0.0, min(1.0, scale_confidence)))
				}

			except Exception as e:
				if self.logger:
					self.logger.warning(f"多尺度分析失败: {str(e)}")
				return self._fallback_analysis()

		def _extract_local_features(self, points1: np.ndarray, points2: np.ndarray, avg_scale: float) -> dict:
			"""提取局部尺度特征 (细节特征)"""
			try:
				# 局部曲率分析
				curvature_sim = self._calculate_curvature_similarity(points1, points2)

				# 边长比例分析
				edge_ratio_sim = self._calculate_edge_ratio_similarity(points1, points2)

				# 角度分布分析
				angle_dist_sim = self._calculate_angle_distribution_similarity(points1, points2)

				# 局部密度分析
				density_sim = self._calculate_local_density_similarity(points1, points2, avg_scale)

				# 局部特征融合 (增强区分度)
				local_similarity = (
					0.4 * curvature_sim +
					0.4 * edge_ratio_sim +
					0.1 * angle_dist_sim +
					0.1 * density_sim
				)

				# 应用非线性变换增强区分度
				local_similarity = self._enhance_discrimination(local_similarity)

				return {
					'curvature_similarity': curvature_sim,
					'edge_ratio_similarity': edge_ratio_sim,
					'angle_distribution_similarity': angle_dist_sim,
					'local_density_similarity': density_sim,
					'local_similarity': local_similarity
				}

			except Exception:
				return self._zero_local_features()

		def _extract_medium_features(self, points1: np.ndarray, points2: np.ndarray, avg_scale: float) -> dict:
			"""提取中等尺度特征 (结构特征)"""
			try:
				# 结构骨架分析
				skeleton_sim = self._calculate_skeleton_similarity(points1, points2)

				# 凸包结构分析
				convex_structure_sim = self._calculate_convex_structure_similarity(points1, points2)

				# 主轴结构分析
				principal_axis_sim = self._calculate_principal_axis_similarity(points1, points2)

				# 区域分割分析
				region_sim = self._calculate_region_similarity(points1, points2, avg_scale)

				# 中等特征融合 (增强区分度)
				medium_similarity = (
					0.4 * skeleton_sim +
					0.3 * convex_structure_sim +
					0.2 * principal_axis_sim +
					0.1 * region_sim
				)

				# 应用非线性变换增强区分度
				medium_similarity = self._enhance_discrimination(medium_similarity)

				return {
					'skeleton_similarity': skeleton_sim,
					'convex_structure_similarity': convex_structure_sim,
					'principal_axis_similarity': principal_axis_sim,
					'region_similarity': region_sim,
					'medium_similarity': medium_similarity
				}

			except Exception:
				return self._zero_medium_features()

		def _extract_global_features(self, points1: np.ndarray, points2: np.ndarray, avg_scale: float) -> dict:
			"""提取全局尺度特征 (整体特征)"""
			try:
				# 整体形状描述符
				shape_descriptor_sim = self._calculate_shape_descriptor_similarity(points1, points2)

				# 全局几何不变量
				invariant_sim = self._calculate_geometric_invariant_similarity(points1, points2)

				# 整体分布特征
				distribution_sim = self._calculate_distribution_similarity(points1, points2)

				# 全局对称性
				global_symmetry_sim = self._calculate_global_symmetry_similarity(points1, points2)

				# 全局特征融合 (增强区分度)
				global_similarity = (
					0.5 * shape_descriptor_sim +
					0.3 * invariant_sim +
					0.15 * distribution_sim +
					0.05 * global_symmetry_sim
				)

				# 应用非线性变换增强区分度
				global_similarity = self._enhance_discrimination(global_similarity)

				return {
					'shape_descriptor_similarity': shape_descriptor_sim,
					'geometric_invariant_similarity': invariant_sim,
					'distribution_similarity': distribution_sim,
					'global_symmetry_similarity': global_symmetry_sim,
					'global_similarity': global_similarity
				}

			except Exception:
				return self._zero_global_features()

		def _fuse_multi_scale(self, local_features: dict, medium_features: dict, global_features: dict) -> tuple:
			"""多尺度特征融合"""
			try:
				# 提取各尺度相似度
				local_sim = local_features['local_similarity']
				medium_sim = medium_features['medium_similarity']
				global_sim = global_features['global_similarity']

				# 计算尺度一致性
				scale_consistency = self._calculate_scale_consistency(local_sim, medium_sim, global_sim)

				# 自适应权重调整
				adjusted_weights = self._adjust_scale_weights(local_sim, medium_sim, global_sim, scale_consistency)

				# 加权融合
				fused_similarity = (
					adjusted_weights['local'] * local_sim +
					adjusted_weights['medium'] * medium_sim +
					adjusted_weights['global'] * global_sim
				)

				# 计算尺度置信度
				scale_confidence = scale_consistency * 0.7 + min(local_sim, medium_sim, global_sim) * 0.3

				return fused_similarity, scale_confidence

			except Exception:
				# 降级到简单平均
				simple_avg = (local_features.get('local_similarity', 0.0) +
							 medium_features.get('medium_similarity', 0.0) +
							 global_features.get('global_similarity', 0.0)) / 3.0
				return simple_avg, 0.3

		def _calculate_scale_consistency(self, local_sim: float, medium_sim: float, global_sim: float) -> float:
			"""计算尺度一致性"""
			try:
				# 计算各尺度间的差异
				diff_lm = abs(local_sim - medium_sim)
				diff_mg = abs(medium_sim - global_sim)
				diff_lg = abs(local_sim - global_sim)

				# 平均差异
				avg_diff = (diff_lm + diff_mg + diff_lg) / 3.0

				# 一致性得分 (差异越小，一致性越高)
				consistency = np.exp(-avg_diff * 3.0)

				return max(0.0, min(1.0, consistency))

			except Exception:
				return 0.5

		def _adjust_scale_weights(self, local_sim: float, medium_sim: float, global_sim: float, consistency: float) -> dict:
			"""自适应调整尺度权重"""
			try:
				# 基础权重
				base_weights = {
					'local': self.local_weight,
					'medium': self.medium_weight,
					'global': self.global_weight
				}

				# 根据一致性调整权重
				if consistency > 0.8:
					# 高一致性：保持均衡权重
					adjusted_weights = base_weights
				elif consistency > 0.5:
					# 中等一致性：增强表现最好的尺度
					best_scale = max([('local', local_sim), ('medium', medium_sim), ('global', global_sim)],
								   key=lambda x: x[1])[0]
					adjusted_weights = base_weights.copy()
					adjusted_weights[best_scale] *= 1.2
				else:
					# 低一致性：更保守的权重分配
					adjusted_weights = {
						'local': 0.25,
						'medium': 0.5,  # 中等尺度更稳定
						'global': 0.25
					}

				# 归一化权重
				total_weight = sum(adjusted_weights.values())
				if total_weight > 0:
					for key in adjusted_weights:
						adjusted_weights[key] /= total_weight

				return adjusted_weights

			except Exception:
				return {'local': 0.3, 'medium': 0.4, 'global': 0.3}

		def _enhance_discrimination(self, similarity: float) -> float:
			"""增强区分度的非线性变换"""
			try:
				# 确保输入在[0,1]范围内
				similarity = max(0.0, min(1.0, similarity))

				# 使用S型曲线增强区分度
				# 当相似度接近0.5时，差异被放大
				# 当相似度接近0或1时，保持稳定

				if similarity < 0.5:
					# 低相似度区域：使用平方函数压缩
					enhanced = 2.0 * (similarity ** 1.5)
				else:
					# 高相似度区域：使用开方函数拉伸
					enhanced = 1.0 - 2.0 * ((1.0 - similarity) ** 1.5)

				return max(0.0, min(1.0, enhanced))

			except Exception:
				return similarity

		# 局部特征计算方法
		def _calculate_curvature_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算曲率相似度"""
			try:
				curvatures1 = self._calculate_curvatures(points1)
				curvatures2 = self._calculate_curvatures(points2)

				if len(curvatures1) == 0 or len(curvatures2) == 0:
					return 0.5

				# 使用直方图比较曲率分布
				hist1, _ = np.histogram(curvatures1, bins=10, range=(-1, 1))
				hist2, _ = np.histogram(curvatures2, bins=10, range=(-1, 1))

				# 归一化
				hist1 = hist1 / (np.sum(hist1) + 1e-10)
				hist2 = hist2 / (np.sum(hist2) + 1e-10)

				# 计算相似度
				similarity = 1.0 - np.sum(np.abs(hist1 - hist2)) / 2.0
				return max(0.0, min(1.0, similarity))

			except Exception:
				return 0.5

		def _calculate_edge_ratio_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算边长比例相似度"""
			try:
				edges1 = self._calculate_edge_lengths(points1)
				edges2 = self._calculate_edge_lengths(points2)

				if len(edges1) == 0 or len(edges2) == 0:
					return 0.5

				# 归一化边长
				edges1_norm = edges1 / (np.max(edges1) + 1e-10)
				edges2_norm = edges2 / (np.max(edges2) + 1e-10)

				# 计算边长比例分布的相似度 (增强区分度)
				if len(edges1_norm) != len(edges2_norm):
					# 不同边数，使用统计特征比较
					stats1 = [np.mean(edges1_norm), np.std(edges1_norm), np.min(edges1_norm), np.max(edges1_norm)]
					stats2 = [np.mean(edges2_norm), np.std(edges2_norm), np.min(edges2_norm), np.max(edges2_norm)]
					diff = np.mean(np.abs(np.array(stats1) - np.array(stats2)))
					# 增强区分度：不同边数的形状相似度降低
					edge_count_penalty = abs(len(edges1_norm) - len(edges2_norm)) / max(len(edges1_norm), len(edges2_norm))
					similarity = np.exp(-diff * 4.0) * (1.0 - edge_count_penalty * 0.3)
				else:
					# 相同边数，直接比较
					diff = np.mean(np.abs(edges1_norm - edges2_norm))
					# 增强敏感度
					similarity = np.exp(-diff * 3.0)

				return max(0.0, min(1.0, similarity))

			except Exception:
				return 0.5

		def _calculate_angle_distribution_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算角度分布相似度"""
			try:
				angles1 = self._calculate_internal_angles(points1)
				angles2 = self._calculate_internal_angles(points2)

				if len(angles1) == 0 or len(angles2) == 0:
					return 0.5

				# 使用直方图比较角度分布
				hist1, _ = np.histogram(angles1, bins=8, range=(0, 2*np.pi))
				hist2, _ = np.histogram(angles2, bins=8, range=(0, 2*np.pi))

				# 归一化
				hist1 = hist1 / (np.sum(hist1) + 1e-10)
				hist2 = hist2 / (np.sum(hist2) + 1e-10)

				# 计算相似度
				similarity = 1.0 - np.sum(np.abs(hist1 - hist2)) / 2.0
				return max(0.0, min(1.0, similarity))

			except Exception:
				return 0.5

		def _calculate_local_density_similarity(self, points1: np.ndarray, points2: np.ndarray, avg_scale: float) -> float:
			"""计算局部密度相似度"""
			try:
				density1 = len(points1) / (avg_scale + 1e-10)
				density2 = len(points2) / (avg_scale + 1e-10)

				# 密度比例相似度
				density_ratio = min(density1, density2) / max(density1, density2)

				return max(0.0, min(1.0, density_ratio))

			except Exception:
				return 0.5

		# 中等特征计算方法
		def _calculate_skeleton_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算结构骨架相似度"""
			try:
				# 简化的骨架分析：使用质心到各点的距离分布
				centroid1 = np.mean(points1, axis=0)
				centroid2 = np.mean(points2, axis=0)

				distances1 = np.sqrt(np.sum((points1 - centroid1)**2, axis=1))
				distances2 = np.sqrt(np.sum((points2 - centroid2)**2, axis=1))

				# 归一化距离
				distances1_norm = distances1 / (np.max(distances1) + 1e-10)
				distances2_norm = distances2 / (np.max(distances2) + 1e-10)

				# 使用直方图比较距离分布
				hist1, _ = np.histogram(distances1_norm, bins=8, range=(0, 1))
				hist2, _ = np.histogram(distances2_norm, bins=8, range=(0, 1))

				# 归一化
				hist1 = hist1 / (np.sum(hist1) + 1e-10)
				hist2 = hist2 / (np.sum(hist2) + 1e-10)

				# 计算相似度
				similarity = 1.0 - np.sum(np.abs(hist1 - hist2)) / 2.0
				return max(0.0, min(1.0, similarity))

			except Exception:
				return 0.5

		def _calculate_convex_structure_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算凸包结构相似度 (增强区分度)"""
			try:
				# 计算凸性比例
				area1 = self._calculate_polygon_area(points1)
				area2 = self._calculate_polygon_area(points2)

				convex_area1 = self._calculate_convex_hull_area(points1)
				convex_area2 = self._calculate_convex_hull_area(points2)

				if convex_area1 > 0 and convex_area2 > 0:
					ratio1 = area1 / convex_area1
					ratio2 = area2 / convex_area2

					# 计算凸性差异
					convexity_diff = abs(ratio1 - ratio2)

					# 计算面积比例差异
					area_ratio = min(area1, area2) / max(area1, area2)

					# 计算凸包面积比例差异
					convex_ratio = min(convex_area1, convex_area2) / max(convex_area1, convex_area2)

					# 综合相似度 (更严格的判断)
					similarity = (
						0.5 * (1.0 - convexity_diff) +
						0.3 * area_ratio +
						0.2 * convex_ratio
					)

					# 应用严格的非线性变换
					similarity = similarity ** 1.5

					return max(0.0, min(1.0, similarity))

				return 0.3  # 降低默认值

			except Exception:
				return 0.3

		def _calculate_principal_axis_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算主轴相似度 (增强区分度)"""
			try:
				angle1 = self._calculate_principal_axis_angle(points1)
				angle2 = self._calculate_principal_axis_angle(points2)

				# 计算角度差异
				angle_diff = abs(angle1 - angle2)
				angle_diff = min(angle_diff, 180 - angle_diff)

				# 计算长宽比
				bbox1 = self._calculate_bbox(points1)
				bbox2 = self._calculate_bbox(points2)

				width1 = bbox1['max_x'] - bbox1['min_x']
				height1 = bbox1['max_y'] - bbox1['min_y']
				width2 = bbox2['max_x'] - bbox2['min_x']
				height2 = bbox2['max_y'] - bbox2['min_y']

				aspect1 = width1 / (height1 + 1e-10)
				aspect2 = width2 / (height2 + 1e-10)

				# 角度相似度
				angle_sim = 1.0 - (angle_diff / 90.0)

				# 长宽比相似度
				aspect_sim = min(aspect1, aspect2) / max(aspect1, aspect2)

				# 综合相似度 (角度和长宽比都重要)
				similarity = 0.6 * angle_sim + 0.4 * aspect_sim

				# 应用严格变换
				similarity = similarity ** 1.3

				return max(0.0, min(1.0, similarity))

			except Exception:
				return 0.3

		def _calculate_region_similarity(self, points1: np.ndarray, points2: np.ndarray, avg_scale: float) -> float:
			"""计算区域相似度"""
			try:
				# 简化实现：使用面积和周长的组合
				area1 = self._calculate_polygon_area(points1)
				area2 = self._calculate_polygon_area(points2)
				perimeter1 = self._calculate_polygon_perimeter(points1)
				perimeter2 = self._calculate_polygon_perimeter(points2)

				if area1 > 0 and area2 > 0 and perimeter1 > 0 and perimeter2 > 0:
					area_ratio = min(area1, area2) / max(area1, area2)
					perimeter_ratio = min(perimeter1, perimeter2) / max(perimeter1, perimeter2)
					similarity = 0.6 * area_ratio + 0.4 * perimeter_ratio
					return max(0.0, min(1.0, similarity))

				return 0.5

			except Exception:
				return 0.5

		# 全局特征计算方法
		def _calculate_shape_descriptor_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算形状描述符相似度"""
			try:
				# 使用简化的形状描述符：面积、周长、紧凑度
				area1 = self._calculate_polygon_area(points1)
				area2 = self._calculate_polygon_area(points2)
				perimeter1 = self._calculate_polygon_perimeter(points1)
				perimeter2 = self._calculate_polygon_perimeter(points2)

				if area1 > 0 and area2 > 0 and perimeter1 > 0 and perimeter2 > 0:
					# 紧凑度
					compactness1 = (4 * np.pi * area1) / (perimeter1 ** 2)
					compactness2 = (4 * np.pi * area2) / (perimeter2 ** 2)

					# 长宽比
					bbox1 = self._calculate_bbox(points1)
					bbox2 = self._calculate_bbox(points2)
					aspect1 = (bbox1['max_x'] - bbox1['min_x']) / (bbox1['max_y'] - bbox1['min_y'] + 1e-10)
					aspect2 = (bbox2['max_x'] - bbox2['min_x']) / (bbox2['max_y'] - bbox2['min_y'] + 1e-10)

					# 相似度计算 (增强区分度)
					compactness_sim = 1.0 - abs(compactness1 - compactness2)
					aspect_sim = min(aspect1, aspect2) / max(aspect1, aspect2)

					# 面积比例相似度
					area_sim = min(area1, area2) / max(area1, area2)

					# 周长比例相似度
					perimeter_sim = min(perimeter1, perimeter2) / max(perimeter1, perimeter2)

					# 综合相似度 (增加更多维度)
					similarity = (
						0.3 * compactness_sim +
						0.3 * aspect_sim +
						0.2 * area_sim +
						0.2 * perimeter_sim
					)

					# 应用严格的非线性变换
					similarity = similarity ** 1.2
					return max(0.0, min(1.0, similarity))

				return 0.5

			except Exception:
				return 0.5

		def _calculate_geometric_invariant_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算几何不变量相似度"""
			try:
				# 使用简化的几何不变量：边数、凸性
				edge_count1 = len(points1)
				edge_count2 = len(points2)

				# 边数相似度
				edge_sim = min(edge_count1, edge_count2) / max(edge_count1, edge_count2)

				# 凸性相似度
				convexity1 = self._calculate_convexity(points1)
				convexity2 = self._calculate_convexity(points2)
				convexity_sim = 1.0 - abs(convexity1 - convexity2)

				similarity = 0.5 * edge_sim + 0.5 * convexity_sim
				return max(0.0, min(1.0, similarity))

			except Exception:
				return 0.5

		def _calculate_distribution_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算分布相似度"""
			try:
				# 使用点的空间分布特征
				centroid1 = np.mean(points1, axis=0)
				centroid2 = np.mean(points2, axis=0)

				# 计算各点到质心的距离分布
				distances1 = np.sqrt(np.sum((points1 - centroid1)**2, axis=1))
				distances2 = np.sqrt(np.sum((points2 - centroid2)**2, axis=1))

				# 归一化
				distances1_norm = distances1 / (np.max(distances1) + 1e-10)
				distances2_norm = distances2 / (np.max(distances2) + 1e-10)

				# 统计特征比较
				mean1, std1 = np.mean(distances1_norm), np.std(distances1_norm)
				mean2, std2 = np.mean(distances2_norm), np.std(distances2_norm)

				mean_diff = abs(mean1 - mean2)
				std_diff = abs(std1 - std2)

				similarity = np.exp(-(mean_diff + std_diff) * 2.0)
				return max(0.0, min(1.0, similarity))

			except Exception:
				return 0.5

		def _calculate_global_symmetry_similarity(self, points1: np.ndarray, points2: np.ndarray) -> float:
			"""计算全局对称性相似度"""
			try:
				# 简化的对称性分析
				symmetry1 = self._calculate_symmetry_score(points1)
				symmetry2 = self._calculate_symmetry_score(points2)

				similarity = 1.0 - abs(symmetry1 - symmetry2)
				return max(0.0, min(1.0, similarity))

			except Exception:
				return 0.5

		# 辅助计算方法
		def _calculate_polygon_area(self, points: np.ndarray) -> float:
			"""计算多边形面积"""
			if len(points) < 3:
				return 0.0

			area = 0.0
			n = len(points)
			for i in range(n):
				j = (i + 1) % n
				area += points[i][0] * points[j][1]
				area -= points[j][0] * points[i][1]
			return abs(area) / 2.0

		def _calculate_polygon_perimeter(self, points: np.ndarray) -> float:
			"""计算多边形周长"""
			if len(points) < 2:
				return 0.0

			perimeter = 0.0
			n = len(points)
			for i in range(n):
				j = (i + 1) % n
				dist = np.sqrt((points[j][0] - points[i][0])**2 + (points[j][1] - points[i][1])**2)
				perimeter += dist
			return perimeter

		def _calculate_convex_hull_area(self, points: np.ndarray) -> float:
			"""计算凸包面积"""
			try:
				from scipy.spatial import ConvexHull
				if len(points) < 3:
					return 0.0
				hull = ConvexHull(points)
				return hull.volume  # 在2D中，volume就是面积
			except Exception:
				# 降级方案：使用边界框面积
				bbox = self._calculate_bbox(points)
				width = bbox['max_x'] - bbox['min_x']
				height = bbox['max_y'] - bbox['min_y']
				return width * height

		def _calculate_bbox(self, points: np.ndarray) -> dict:
			"""计算边界框"""
			xs = points[:, 0]
			ys = points[:, 1]
			return {
				'min_x': float(np.min(xs)), 'max_x': float(np.max(xs)),
				'min_y': float(np.min(ys)), 'max_y': float(np.max(ys))
			}

		def _calculate_principal_axis_angle(self, points: np.ndarray) -> float:
			"""计算主轴角度"""
			try:
				# 计算质心
				centroid = np.mean(points, axis=0)

				# 计算协方差矩阵
				centered_points = points - centroid
				cov_matrix = np.cov(centered_points.T)

				# 计算特征值和特征向量
				eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

				# 找到最大特征值对应的特征向量（主轴方向）
				max_eigenvalue_index = np.argmax(eigenvalues)
				principal_axis = eigenvectors[:, max_eigenvalue_index]

				# 计算角度（弧度转角度）
				angle = np.arctan2(principal_axis[1], principal_axis[0]) * 180 / np.pi

				# 确保角度在0-180度范围内
				if angle < 0:
					angle += 180

				return float(angle)

			except Exception:
				return 0.0

		def _calculate_convexity(self, points: np.ndarray) -> float:
			"""计算凸性"""
			try:
				area = self._calculate_polygon_area(points)
				convex_area = self._calculate_convex_hull_area(points)

				if convex_area > 0:
					return area / convex_area
				else:
					return 0.0
			except Exception:
				return 0.0

		def _calculate_symmetry_score(self, points: np.ndarray) -> float:
			"""计算对称性得分"""
			try:
				# 简化的对称性分析：计算水平和垂直对称性
				centroid = np.mean(points, axis=0)

				# 水平对称性
				h_symmetry = 0.0
				for point in points:
					symmetric_y = 2 * centroid[1] - point[1]
					min_dist = float('inf')
					for other_point in points:
						dist = abs(other_point[1] - symmetric_y) + abs(other_point[0] - point[0])
						min_dist = min(min_dist, dist)

					char_scale = self._calculate_characteristic_scale(points)
					if char_scale > 0:
						h_symmetry += np.exp(-min_dist / (char_scale * 0.1))

				h_symmetry /= len(points) if len(points) > 0 else 1

				# 垂直对称性
				v_symmetry = 0.0
				for point in points:
					symmetric_x = 2 * centroid[0] - point[0]
					min_dist = float('inf')
					for other_point in points:
						dist = abs(other_point[0] - symmetric_x) + abs(other_point[1] - point[1])
						min_dist = min(min_dist, dist)

					char_scale = self._calculate_characteristic_scale(points)
					if char_scale > 0:
						v_symmetry += np.exp(-min_dist / (char_scale * 0.1))

				v_symmetry /= len(points) if len(points) > 0 else 1

				# 综合对称性
				return (h_symmetry + v_symmetry) / 2.0

			except Exception:
				return 0.5

		# 辅助计算方法
		def _calculate_curvatures(self, points: np.ndarray) -> np.ndarray:
			"""计算各点的曲率"""
			try:
				if len(points) < 3:
					return np.array([])

				curvatures = []
				n = len(points)

				for i in range(n):
					# 取前后相邻点
					prev_idx = (i - 1) % n
					next_idx = (i + 1) % n

					p1 = points[prev_idx]
					p2 = points[i]
					p3 = points[next_idx]

					# 计算曲率
					curvature = self._calculate_point_curvature(p1, p2, p3)
					curvatures.append(curvature)

				return np.array(curvatures)

			except Exception:
				return np.array([])

		def _calculate_point_curvature(self, p1: np.ndarray, p2: np.ndarray, p3: np.ndarray) -> float:
			"""计算三点的曲率"""
			try:
				# 向量
				v1 = p2 - p1
				v2 = p3 - p2

				# 长度
				len1 = np.linalg.norm(v1)
				len2 = np.linalg.norm(v2)

				if len1 < 1e-10 or len2 < 1e-10:
					return 0.0

				# 归一化
				v1_norm = v1 / len1
				v2_norm = v2 / len2

				# 计算角度变化
				dot_product = np.clip(np.dot(v1_norm, v2_norm), -1.0, 1.0)
				angle_change = np.arccos(dot_product)

				# 曲率 = 角度变化 / 平均弧长
				avg_length = (len1 + len2) / 2.0
				curvature = angle_change / (avg_length + 1e-10)

				return curvature

			except Exception:
				return 0.0

		def _calculate_edge_lengths(self, points: np.ndarray) -> np.ndarray:
			"""计算边长"""
			try:
				if len(points) < 2:
					return np.array([])

				edges = []
				n = len(points)

				for i in range(n):
					next_idx = (i + 1) % n
					edge_length = np.linalg.norm(points[next_idx] - points[i])
					edges.append(edge_length)

				return np.array(edges)

			except Exception:
				return np.array([])

		def _calculate_internal_angles(self, points: np.ndarray) -> np.ndarray:
			"""计算内角"""
			try:
				if len(points) < 3:
					return np.array([])

				angles = []
				n = len(points)

				for i in range(n):
					prev_idx = (i - 1) % n
					next_idx = (i + 1) % n

					p1 = points[prev_idx]
					p2 = points[i]
					p3 = points[next_idx]

					# 计算内角
					angle = self._calculate_internal_angle(p1, p2, p3)
					angles.append(angle)

				return np.array(angles)

			except Exception:
				return np.array([])

		def _calculate_internal_angle(self, p1: np.ndarray, p2: np.ndarray, p3: np.ndarray) -> float:
			"""计算三点形成的内角"""
			try:
				# 向量
				v1 = p1 - p2
				v2 = p3 - p2

				# 长度
				len1 = np.linalg.norm(v1)
				len2 = np.linalg.norm(v2)

				if len1 < 1e-10 or len2 < 1e-10:
					return 0.0

				# 计算角度
				dot_product = np.clip(np.dot(v1, v2) / (len1 * len2), -1.0, 1.0)
				angle = np.arccos(dot_product)

				return angle

			except Exception:
				return 0.0

		def _calculate_characteristic_scale(self, points: np.ndarray) -> float:
			"""计算特征尺度"""
			try:
				if len(points) < 2:
					return 1.0

				xs = points[:, 0]
				ys = points[:, 1]
				width = float(np.max(xs) - np.min(xs))
				height = float(np.max(ys) - np.min(ys))
				return (width ** 2 + height ** 2) ** 0.5

			except Exception:
				return 1.0

		# 零结果方法
		def _zero_local_features(self) -> dict:
			"""返回零局部特征"""
			return {
				'curvature_similarity': 0.0,
				'edge_ratio_similarity': 0.0,
				'angle_distribution_similarity': 0.0,
				'local_density_similarity': 0.0,
				'local_similarity': 0.0
			}

		def _zero_medium_features(self) -> dict:
			"""返回零中等特征"""
			return {
				'skeleton_similarity': 0.0,
				'convex_structure_similarity': 0.0,
				'principal_axis_similarity': 0.0,
				'region_similarity': 0.0,
				'medium_similarity': 0.0
			}

		def _zero_global_features(self) -> dict:
			"""返回零全局特征"""
			return {
				'shape_descriptor_similarity': 0.0,
				'geometric_invariant_similarity': 0.0,
				'distribution_similarity': 0.0,
				'global_symmetry_similarity': 0.0,
				'global_similarity': 0.0
			}

		def _fallback_analysis(self) -> dict:
			"""降级分析结果"""
			return {
				'local_features': self._zero_local_features(),
				'medium_features': self._zero_medium_features(),
				'global_features': self._zero_global_features(),
				'fused_similarity': 0.0,
				'scale_confidence': 0.0
			}

	class DecisionEngine:
		"""
		新决策引擎 (新架构)

		原则：简单、可配置、可解释
		功能：替换复杂的五层决策机制
		特点：基于融合相似度和置信度的智能决策
		"""

		def __init__(self, config: dict, logger=None):
			self.high_threshold = config.get('high_threshold', 0.75)
			self.low_threshold = config.get('low_threshold', 0.25)
			self.confidence_threshold = config.get('confidence_threshold', 0.6)
			self.medium_threshold_upper = config.get('medium_threshold_upper', 0.6)
			self.medium_threshold_lower = config.get('medium_threshold_lower', 0.4)
			self.logger = logger

		def decide(self, fusion_result: dict, user_threshold: float = None) -> dict:
			"""
			做出最终决策

			参数:
				fusion_result: 自适应融合的结果
				user_threshold: 用户指定的相似度阈值

			返回:
				{
					'action': str,          # 'skip' 或 'add'
					'reason': str,          # 决策原因
					'confidence': float,    # 决策置信度
					'details': dict         # 详细信息
				}
			"""
			try:
				similarity = fusion_result['similarity']
				confidence = fusion_result['confidence']

				# 用户阈值优先检查
				if user_threshold is not None:
					if similarity < user_threshold:
						return {
							'action': 'add',
							'reason': f'低于用户阈值 (相似度={similarity:.3f} < {user_threshold:.3f})',
							'confidence': confidence,
							'details': {
								'decision_layer': 'user_threshold',
								'threshold_used': user_threshold,
								'similarity': similarity,
								'fusion_weights': fusion_result.get('weights', {}),
								'fusion_reason': fusion_result.get('fusion_reason', '')
							}
						}

				# 简化的三层决策
				if similarity >= self.high_threshold:
					return {
						'action': 'skip',
						'reason': f'高相似度 (相似度={similarity:.3f} ≥ {self.high_threshold})',
						'confidence': confidence,
						'details': {
							'decision_layer': 'high_similarity',
							'threshold_used': self.high_threshold,
							'similarity': similarity,
							'fusion_weights': fusion_result.get('weights', {}),
							'fusion_reason': fusion_result.get('fusion_reason', '')
						}
					}
				elif similarity <= self.low_threshold:
					return {
						'action': 'add',
						'reason': f'低相似度 (相似度={similarity:.3f} ≤ {self.low_threshold})',
						'confidence': confidence,
						'details': {
							'decision_layer': 'low_similarity',
							'threshold_used': self.low_threshold,
							'similarity': similarity,
							'fusion_weights': fusion_result.get('weights', {}),
							'fusion_reason': fusion_result.get('fusion_reason', '')
						}
					}
				else:
					# 中等相似度，基于置信度和细分阈值决策
					return self._decide_medium_similarity(similarity, confidence, fusion_result)

			except Exception as e:
				if self.logger:
					self.logger.warning(f"决策引擎失败: {str(e)}")
				return self._fallback_decision(fusion_result)

		def _decide_medium_similarity(self, similarity: float, confidence: float, fusion_result: dict) -> dict:
			"""中等相似度的决策逻辑"""
			try:
				# 基于置信度的决策
				if confidence >= self.confidence_threshold:
					# 高置信度时，进一步细分相似度区间
					if similarity >= self.medium_threshold_upper:
						action = 'skip'
						reason = f'中高相似度，高置信度 (相似度={similarity:.3f}, 置信度={confidence:.3f})'
					elif similarity <= self.medium_threshold_lower:
						action = 'add'
						reason = f'中低相似度，高置信度 (相似度={similarity:.3f}, 置信度={confidence:.3f})'
					else:
						# 中等相似度，高置信度，倾向于跳过
						action = 'skip'
						reason = f'中等相似度，高置信度，倾向跳过 (相似度={similarity:.3f}, 置信度={confidence:.3f})'
				else:
					# 低置信度时，倾向于追加（保守策略）
					action = 'add'
					reason = f'中等相似度，低置信度，保守追加 (相似度={similarity:.3f}, 置信度={confidence:.3f})'

				return {
					'action': action,
					'reason': reason,
					'confidence': confidence,
					'details': {
						'decision_layer': 'medium_similarity',
						'confidence_based': True,
						'similarity': similarity,
						'confidence_threshold': self.confidence_threshold,
						'fusion_weights': fusion_result.get('weights', {}),
						'fusion_reason': fusion_result.get('fusion_reason', '')
					}
				}

			except Exception:
				# 中等相似度决策失败时，默认追加
				return {
					'action': 'add',
					'reason': f'中等相似度决策失败，默认追加 (相似度={similarity:.3f})',
					'confidence': 0.3,
					'details': {'decision_layer': 'fallback_medium'}
				}

		def _fallback_decision(self, fusion_result: dict) -> dict:
			"""降级决策方案"""
			try:
				similarity = fusion_result.get('similarity', 0.0)

				# 简单的阈值决策
				if similarity > 0.5:
					action = 'skip'
					reason = '降级决策：相似度>0.5，跳过'
				else:
					action = 'add'
					reason = '降级决策：相似度≤0.5，追加'

				return {
					'action': action,
					'reason': reason,
					'confidence': 0.2,  # 降级决策置信度较低
					'details': {
						'decision_layer': 'fallback',
						'similarity': similarity,
						'fusion_result': fusion_result
					}
				}

			except Exception:
				# 最终降级：默认追加
				return {
					'action': 'add',
					'reason': '决策引擎完全失败，默认追加',
					'confidence': 0.0,
					'details': {'decision_layer': 'emergency_fallback'}
				}

	def __preprocess_coordinates( self, points: List[ List[ float ] ] ) -> Optional[ np.ndarray ]:
		"""
		坐标预处理和标准化

		处理步骤:
		1. 格式统一 - 转换为numpy数组
		2. 异常值处理 - 过滤无穷大、NaN值
		3. 坐标排序 - 确保顺时针或逆时针顺序
		4. 数据验证 - 检查几何有效性

		参数:
			points: 原始坐标点列表

		返回:
			预处理后的numpy数组，失败返回None
		"""
		try:
			# 检查输入有效性 (支持numpy数组)
			if points is None:
				return None

			# 转换为numpy数组
			points_array = np.array( points, dtype=np.float64 )

			if len( points_array ) < 3:
				return None

			# 检查和过滤异常值
			if np.any( np.isnan( points_array ) ) or np.any( np.isinf( points_array ) ):
				# 过滤掉包含NaN或无穷大的点
				valid_mask = ~(np.isnan( points_array ).any( axis=1 ) | np.isinf( points_array ).any( axis=1 ))
				points_array = points_array[ valid_mask ]

				if len( points_array ) < 3:
					return None

			# 确保坐标顺序 (使用简化的顺序检查)
			# 计算多边形的有向面积，如果为负则逆时针，为正则顺时针
			signed_area = 0.0
			n = len( points_array )
			for i in range( n ):
				j = (i + 1) % n
				signed_area += (points_array[ j ][ 0 ] - points_array[ i ][ 0 ]) * (points_array[ j ][ 1 ] + points_array[ i ][ 1 ])

			# 如果有向面积为负，说明是逆时针，需要反转
			if signed_area < 0:
				points_array = points_array[ ::-1 ]

			return points_array

		except Exception as e:
			self.__logger.warning( f"坐标预处理失败: {str( e )}" )
			return None

	def __calculate_geometric_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""
		计算几何相似度 (权重: 35%)

		基于Shapely库的精确几何计算，包含：
		1. IoU (Intersection over Union) - 40%权重
		2. IoS (Intersection over Smaller) - 30%权重
		3. 包含关系检测 - 20%权重
		4. 几何中心距离 - 10%权重

		公式: 几何相似度 = 0.4×IoU + 0.3×IoS + 0.2×包含关系 + 0.1×中心距离

		参数:
			points1: 第一个多边形的坐标点
			points2: 第二个多边形的坐标点

		返回:
			几何相似度 (0.0-1.0)
		"""
		try:
			# 优先使用Shapely库进行精确几何计算
			try:
				from shapely.geometry import Polygon
				from shapely.validation import make_valid

				# 创建多边形对象
				poly1 = Polygon( points1 )
				poly2 = Polygon( points2 )

				# 验证和修复多边形
				if not poly1.is_valid:
					poly1 = make_valid( poly1 )
				if not poly2.is_valid:
					poly2 = make_valid( poly2 )

				# 如果修复后仍然无效，使用buffer方法
				if not poly1.is_valid:
					poly1 = poly1.buffer( 0 )
				if not poly2.is_valid:
					poly2 = poly2.buffer( 0 )

				# 计算面积
				area1 = poly1.area
				area2 = poly2.area

				if area1 < self.__min_area_threshold or area2 < self.__min_area_threshold:
					return 0.0

				# 计算交集和并集
				try:
					intersection = poly1.intersection( poly2 )
					union = poly1.union( poly2 )
					intersection_area = intersection.area if hasattr( intersection, 'area' ) else 0.0
					union_area = union.area if hasattr( union, 'area' ) else (area1 + area2)
				except Exception:
					intersection_area = 0.0
					union_area = area1 + area2

				# 1. IoU相似度 (40%权重)
				iou_similarity = intersection_area / union_area if union_area > 0 else 0.0

				# 2. IoS相似度 (30%权重) - 针对大小差异显著的情况
				smaller_area = min( area1, area2 )
				ios_similarity = intersection_area / smaller_area if smaller_area > 0 else 0.0

				# 3. 包含关系检测 (20%权重)
				containment_score = self.__calculate_containment_relationship( poly1, poly2, intersection_area, area1, area2 )

				# 4. 几何中心距离相似度 (10%权重)
				center_similarity = self.__calculate_geometric_center_similarity( poly1, poly2 )

				# 综合几何相似度
				geometric_similarity = (
						0.4 * iou_similarity +
						0.3 * ios_similarity +
						0.2 * containment_score +
						0.1 * center_similarity
				)

				return float( max( 0.0, min( 1.0, geometric_similarity ) ) )

			except ImportError:
				# Shapely不可用时的降级方案
				self.__logger.warning( "Shapely库不可用，使用降级几何计算方案" )
				return self.__calculate_geometric_similarity_fallback( points1, points2 )

		except Exception as e:
			self.__logger.warning( f"几何相似度计算失败: {str( e )}" )
			return 0.0

	def __calculate_containment_relationship(
			self, poly1, poly2, intersection_area: float,
			area1: float, area2: float
	) -> float:
		"""
		计算包含关系得分

		包含关系类型:
		1. 完全包含 - 一个多边形完全包含另一个 (得分1.0)
		2. 部分包含 - 有显著重叠但不完全包含 (得分0.5-0.9)
		3. 相交 - 有重叠但包含程度较低 (得分0.1-0.5)
		4. 分离 - 无重叠 (得分0.0)

		参数:
			poly1, poly2: Shapely多边形对象
			intersection_area: 交集面积
			area1, area2: 两个多边形的面积

		返回:
			包含关系得分 (0.0-1.0)
		"""
		try:
			if intersection_area <= 0:
				return 0.0  # 完全分离

			# 计算包含比例
			contain_ratio1 = intersection_area / area1 if area1 > 0 else 0.0
			contain_ratio2 = intersection_area / area2 if area2 > 0 else 0.0
			max_contain_ratio = max( contain_ratio1, contain_ratio2 )

			# 根据包含比例确定得分
			if max_contain_ratio >= 0.95:
				return 1.0  # 完全包含
			elif max_contain_ratio >= 0.80:
				return 0.8 + 0.2 * (max_contain_ratio - 0.80) / 0.15  # 高度包含
			elif max_contain_ratio >= 0.50:
				return 0.5 + 0.3 * (max_contain_ratio - 0.50) / 0.30  # 部分包含
			elif max_contain_ratio >= 0.20:
				return 0.1 + 0.4 * (max_contain_ratio - 0.20) / 0.30  # 相交
			else:
				return max_contain_ratio * 0.5  # 轻微相交

		except Exception:
			return 0.0

	def __calculate_geometric_center_similarity( self, poly1, poly2 ) -> float:
		"""
		计算几何中心距离相似度

		使用自适应距离阈值，考虑多边形的尺寸：
		距离阈值 = max(min(bbox_diagonal × 0.1, 50), 10)
		相似度 = exp(-distance / 距离阈值)

		参数:
			poly1, poly2: Shapely多边形对象

		返回:
			中心距离相似度 (0.0-1.0)
		"""
		try:
			# 获取几何中心
			center1 = poly1.centroid
			center2 = poly2.centroid

			# 计算中心距离
			distance = np.sqrt( (center1.x - center2.x) ** 2 + (center1.y - center2.y) ** 2 )

			# 计算自适应距离阈值
			bounds1 = poly1.bounds  # (minx, miny, maxx, maxy)
			bounds2 = poly2.bounds
			diagonal1 = np.sqrt( (bounds1[ 2 ] - bounds1[ 0 ]) ** 2 + (bounds1[ 3 ] - bounds1[ 1 ]) ** 2 )
			diagonal2 = np.sqrt( (bounds2[ 2 ] - bounds2[ 0 ]) ** 2 + (bounds2[ 3 ] - bounds2[ 1 ]) ** 2 )
			avg_diagonal = (diagonal1 + diagonal2) / 2

			# 自适应阈值：bbox_diagonal × 0.1，范围在[10, 50]之间
			distance_threshold = max( min( avg_diagonal * self.__distance_threshold_factor, 50 ), 10 )

			# 计算相似度
			center_similarity = np.exp( -distance / distance_threshold )

			return float( max( 0.0, min( 1.0, center_similarity ) ) )

		except Exception:
			return 0.0

	def __calculate_geometric_similarity_fallback( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""
		几何相似度计算的降级方案 (Shapely不可用时)

		使用纯NumPy实现的简化几何计算：
		1. 简化IoU计算 - 基于边界框重叠
		2. 面积比例相似度
		3. 中心距离相似度

		参数:
			points1, points2: 坐标点数组

		返回:
			几何相似度 (0.0-1.0)
		"""
		try:
			# 计算多边形面积
			area1 = self.__calculate_polygon_area( points1 )
			area2 = self.__calculate_polygon_area( points2 )

			if area1 < self.__min_area_threshold or area2 < self.__min_area_threshold:
				return 0.0

			# 计算边界框
			bbox1 = self.__calculate_bounding_box( points1 )
			bbox2 = self.__calculate_bounding_box( points2 )

			# 1. 边界框重叠相似度 (代替IoU)
			bbox_overlap = self.__calculate_bbox_overlap_similarity( bbox1, bbox2 )

			# 2. 面积比例相似度
			area_ratio = min( area1, area2 ) / max( area1, area2 ) if max( area1, area2 ) > 0 else 0.0

			# 3. 中心距离相似度
			center1 = self.__calculate_polygon_centroid( points1 )
			center2 = self.__calculate_polygon_centroid( points2 )
			center_distance = np.sqrt( (center1[ 0 ] - center2[ 0 ]) ** 2 + (center1[ 1 ] - center2[ 1 ]) ** 2 )

			# 计算自适应距离阈值
			diagonal1 = np.sqrt( (bbox1[ 'max_x' ] - bbox1[ 'min_x' ]) ** 2 + (bbox1[ 'max_y' ] - bbox1[ 'min_y' ]) ** 2 )
			diagonal2 = np.sqrt( (bbox2[ 'max_x' ] - bbox2[ 'min_x' ]) ** 2 + (bbox2[ 'max_y' ] - bbox2[ 'min_y' ]) ** 2 )
			avg_diagonal = (diagonal1 + diagonal2) / 2
			distance_threshold = max( min( avg_diagonal * 0.1, 50 ), 10 )
			center_similarity = np.exp( -center_distance / distance_threshold )

			# 综合几何相似度 (降级方案权重)
			geometric_similarity = (
					0.5 * bbox_overlap +  # 边界框重叠权重50%
					0.3 * area_ratio +  # 面积比例权重30%
					0.2 * center_similarity  # 中心距离权重20%
			)

			return float( max( 0.0, min( 1.0, geometric_similarity ) ) )

		except Exception as e:
			self.__logger.warning( f"降级几何相似度计算失败: {str( e )}" )
			return 0.0

	def __calculate_position_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""
		计算位置相似度 (权重: 25%)

		多层次位置分析：
		1. 绝对位置相似度 - 基于坐标系的直接距离计算 (40%权重)
		2. 相对位置相似度 - 考虑图像尺寸的归一化距离 (30%权重)
		3. 边界框位置相似度 - AABB包围盒的位置关系 (20%权重)
		4. 质心偏移分析 - 几何中心的相对偏移量 (10%权重)

		参数:
			points1, points2: 坐标点数组

		返回:
			位置相似度 (0.0-1.0)
		"""
		try:
			# 计算质心
			centroid1 = self.__calculate_polygon_centroid( points1 )
			centroid2 = self.__calculate_polygon_centroid( points2 )

			# 计算边界框
			bbox1 = self.__calculate_bounding_box( points1 )
			bbox2 = self.__calculate_bounding_box( points2 )

			# 1. 绝对位置相似度 (40%权重)
			absolute_distance = np.sqrt( (centroid1[ 0 ] - centroid2[ 0 ]) ** 2 + (centroid1[ 1 ] - centroid2[ 1 ]) ** 2 )

			# 计算自适应距离阈值
			diagonal1 = np.sqrt( (bbox1[ 'max_x' ] - bbox1[ 'min_x' ]) ** 2 + (bbox1[ 'max_y' ] - bbox1[ 'min_y' ]) ** 2 )
			diagonal2 = np.sqrt( (bbox2[ 'max_x' ] - bbox2[ 'min_x' ]) ** 2 + (bbox2[ 'max_y' ] - bbox2[ 'min_y' ]) ** 2 )
			avg_diagonal = (diagonal1 + diagonal2) / 2
			distance_threshold = max( min( avg_diagonal * 0.1, 50 ), 10 )

			absolute_similarity = np.exp( -absolute_distance / distance_threshold )

			# 2. 相对位置相似度 (30%权重) - 考虑多边形尺寸的归一化
			# 使用边界框对角线长度作为归一化因子
			normalized_distance = absolute_distance / avg_diagonal if avg_diagonal > 0 else 0.0
			relative_similarity = np.exp( -normalized_distance * 5.0 )  # 5.0是经验参数

			# 3. 边界框位置相似度 (20%权重)
			bbox_center1 = ((bbox1[ 'min_x' ] + bbox1[ 'max_x' ]) / 2, (bbox1[ 'min_y' ] + bbox1[ 'max_y' ]) / 2)
			bbox_center2 = ((bbox2[ 'min_x' ] + bbox2[ 'max_x' ]) / 2, (bbox2[ 'min_y' ] + bbox2[ 'max_y' ]) / 2)
			bbox_distance = np.sqrt( (bbox_center1[ 0 ] - bbox_center2[ 0 ]) ** 2 + (bbox_center1[ 1 ] - bbox_center2[ 1 ]) ** 2 )
			bbox_similarity = np.exp( -bbox_distance / distance_threshold )

			# 4. 质心偏移分析 (10%权重) - 质心相对于边界框中心的偏移
			offset1 = (centroid1[ 0 ] - bbox_center1[ 0 ], centroid1[ 1 ] - bbox_center1[ 1 ])
			offset2 = (centroid2[ 0 ] - bbox_center2[ 0 ], centroid2[ 1 ] - bbox_center2[ 1 ])
			offset_distance = np.sqrt( (offset1[ 0 ] - offset2[ 0 ]) ** 2 + (offset1[ 1 ] - offset2[ 1 ]) ** 2 )
			offset_similarity = np.exp( -offset_distance / (avg_diagonal * 0.1) ) if avg_diagonal > 0 else 1.0

			# 综合位置相似度
			position_similarity = (
					0.4 * absolute_similarity +  # 绝对位置权重40%
					0.3 * relative_similarity +  # 相对位置权重30%
					0.2 * bbox_similarity +  # 边界框位置权重20%
					0.1 * offset_similarity  # 质心偏移权重10%
			)

			return float( max( 0.0, min( 1.0, position_similarity ) ) )

		except Exception as e:
			self.__logger.warning( f"位置相似度计算失败: {str( e )}" )
			return 0.0

	def __calculate_scale_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""
		计算尺度相似度 (权重: 20%)

		多尺度特征分析：
		1. 面积比例相似度 - 两个多边形的面积比值 (40%权重)
		2. 周长比例相似度 - 周长的比值关系 (30%权重)
		3. 边界框尺度相似度 - 宽高比的一致性 (20%权重)
		4. 形状复杂度相似度 - 基于凸包面积比的复杂度 (10%权重)

		尺度归一化策略:
		尺度相似度 = 1 - |log(ratio)| / log_threshold
		其中 log_threshold = 2.0 (允许4倍的差异)

		参数:
			points1, points2: 坐标点数组

		返回:
			尺度相似度 (0.0-1.0)
		"""
		try:
			# 计算基本几何特征
			area1 = self.__calculate_polygon_area( points1 )
			area2 = self.__calculate_polygon_area( points2 )
			perimeter1 = self.__calculate_polygon_perimeter( points1 )
			perimeter2 = self.__calculate_polygon_perimeter( points2 )

			if area1 < self.__min_area_threshold or area2 < self.__min_area_threshold:
				return 0.0

			# 计算边界框
			bbox1 = self.__calculate_bounding_box( points1 )
			bbox2 = self.__calculate_bounding_box( points2 )

			# 1. 面积比例相似度 (40%权重)
			area_ratio = max( area1, area2 ) / min( area1, area2 ) if min( area1, area2 ) > 0 else float( 'inf' )
			if area_ratio == float( 'inf' ) or area_ratio > 100:  # 极端比例
				area_similarity = 0.0
			else:
				area_similarity = 1.0 - min( 1.0, abs( np.log( area_ratio ) ) / self.__log_threshold )

			# 2. 周长比例相似度 (30%权重)
			if perimeter1 > 0 and perimeter2 > 0:
				perimeter_ratio = max( perimeter1, perimeter2 ) / min( perimeter1, perimeter2 )
				if perimeter_ratio > 100:  # 极端比例
					perimeter_similarity = 0.0
				else:
					perimeter_similarity = 1.0 - min( 1.0, abs( np.log( perimeter_ratio ) ) / self.__log_threshold )
			else:
				perimeter_similarity = 0.0

			# 3. 边界框尺度相似度 (20%权重)
			width1 = bbox1[ 'max_x' ] - bbox1[ 'min_x' ]
			height1 = bbox1[ 'max_y' ] - bbox1[ 'min_y' ]
			width2 = bbox2[ 'max_x' ] - bbox2[ 'min_x' ]
			height2 = bbox2[ 'max_y' ] - bbox2[ 'min_y' ]

			if width1 > 0 and height1 > 0 and width2 > 0 and height2 > 0:
				aspect_ratio1 = width1 / height1
				aspect_ratio2 = width2 / height2
				aspect_ratio_diff = abs( aspect_ratio1 - aspect_ratio2 ) / max( aspect_ratio1, aspect_ratio2 )
				bbox_similarity = 1.0 - min( 1.0, aspect_ratio_diff )
			else:
				bbox_similarity = 0.0

			# 4. 形状复杂度相似度 (10%权重) - 基于凸包面积比
			try:
				convex_area1 = self.__calculate_convex_hull_area( points1 )
				convex_area2 = self.__calculate_convex_hull_area( points2 )

				if convex_area1 > 0 and convex_area2 > 0:
					complexity1 = area1 / convex_area1  # 多边形面积与凸包面积的比值
					complexity2 = area2 / convex_area2
					complexity_diff = abs( complexity1 - complexity2 )
					complexity_similarity = 1.0 - min( 1.0, complexity_diff )
				else:
					complexity_similarity = 0.0
			except Exception:
				complexity_similarity = 0.0

			# 综合尺度相似度
			scale_similarity = (
					0.4 * area_similarity +  # 面积比例权重40%
					0.3 * perimeter_similarity +  # 周长比例权重30%
					0.2 * bbox_similarity +  # 边界框尺度权重20%
					0.1 * complexity_similarity  # 形状复杂度权重10%
			)

			return float( max( 0.0, min( 1.0, scale_similarity ) ) )

		except Exception as e:
			self.__logger.warning( f"尺度相似度计算失败: {str( e )}" )
			return 0.0

	def __calculate_shape_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""
		计算形状相似度 (权重: 20%)

		基于Hausdorff距离的形状匹配：
		1. 双向Hausdorff距离 - 使用scipy.spatial.distance.directed_hausdorff (40%权重)
		2. 轮廓匹配度 - 使用OpenCV的matchShapes方法 (30%权重)
		3. 凸包相似度 - 凸包形状的一致性分析 (20%权重)
		4. 形状描述符相似度 - 基于傅里叶描述符的形状特征 (10%权重)

		参数:
			points1, points2: 坐标点数组

		返回:
			形状相似度 (0.0-1.0)
		"""
		try:
			# 1. 双向Hausdorff距离 (40%权重)
			hausdorff_similarity = self.__calculate_hausdorff_similarity( points1, points2 )

			# 2. 轮廓匹配度 (30%权重)
			contour_similarity = self.__calculate_contour_matching_similarity( points1, points2 )

			# 3. 凸包相似度 (20%权重)
			convex_similarity = self.__calculate_convex_hull_similarity( points1, points2 )

			# 4. 形状描述符相似度 (10%权重)
			descriptor_similarity = self.__calculate_shape_descriptor_similarity( points1, points2 )

			# 综合形状相似度
			shape_similarity = (
					0.4 * hausdorff_similarity +  # Hausdorff距离权重40%
					0.3 * contour_similarity +  # 轮廓匹配权重30%
					0.2 * convex_similarity +  # 凸包相似度权重20%
					0.1 * descriptor_similarity  # 描述符相似度权重10%
			)

			return float( max( 0.0, min( 1.0, shape_similarity ) ) )

		except Exception as e:
			self.__logger.warning( f"形状相似度计算失败: {str( e )}" )
			return 0.0



	def __check_prerequisites( self, scale_sim: float, shape_sim: float ) -> Tuple[ bool, str ]:
		"""
		检查尺度和形状相似度前提条件 (新架构)

		新架构中的前提条件检查机制：
		- 只影响几何相似度的计算，不影响位置相似度
		- 当前提条件不满足时，几何相似度设为0.0，位置相似度正常计算
		- 这样可以避免对明显不同尺寸或形状的标注进行复杂的几何计算
		- 但仍然保留位置信息用于综合相似度计算

		前提条件阈值:
		- 尺度相似度 ≥ 40% (self.__scale_prerequisite_threshold)
		- 形状相似度 ≥ 30% (self.__shape_prerequisite_threshold)

		参数:
			scale_sim: 尺度相似度 (0.0-1.0)
			shape_sim: 形状相似度 (0.0-1.0)

		返回:
			(是否满足前提条件, 不满足的原因或满足的说明)

		使用示例:
			prerequisites_met, reason = self.__check_prerequisites(0.35, 0.25)
			if not prerequisites_met:
				# 前提条件不满足，几何相似度将设为0.0
				geometric_sim = 0.0
			else:
				# 前提条件满足，正常计算几何相似度
				geometric_sim = self.__calculate_geometric_similarity(...)
		"""
		try:
			# 检查尺度相似度前提条件
			if scale_sim < self.__scale_prerequisite_threshold:
				return False, f'尺度差异过大 (尺度相似度={scale_sim:.3f} < 阈值={self.__scale_prerequisite_threshold:.3f})'

			# 检查形状相似度前提条件
			if shape_sim < self.__shape_prerequisite_threshold:
				return False, f'形状差异过大 (形状相似度={shape_sim:.3f} < 阈值={self.__shape_prerequisite_threshold:.3f})'

			# 两个前提条件都满足
			return True, f'前提条件满足 (尺度={scale_sim:.3f}, 形状={shape_sim:.3f})'

		except Exception as e:
			self.__logger.warning( f"前提条件检查失败: {str( e )}" )
			return True, f'前提条件检查异常，默认通过: {str( e )}'  # 异常时默认通过

	def __make_intelligent_decision_new_architecture(
			self, geometric_sim: float, position_sim: float,
			comprehensive_sim: float
	) -> Tuple[ str, str, str ]:
		"""
		新架构的五层智能决策机制

		基于新的计算架构：前提条件(尺度+形状) → 核心计算(几何+位置) → 综合评估
		决策完全基于几何和位置相似度，尺度和形状相似度仅作为前提条件使用

		决策层级:
		1. 第二层：高相似度检查 - 综合相似度≥0.85或几何/位置单维度极高
		2. 第四层：低相似度确认 - 综合相似度≤0.30或关键维度否决
		3. 第三层：中等相似度细化判断 - 几何和位置维度一致性验证
		4. 第五层：边界情况处理 - 模糊区间精细化判断

		参数:
			geometric_sim: 几何相似度 (核心维度，可能为0.0如果前提条件不满足)
			position_sim: 位置相似度 (核心维度，始终正常计算)
			comprehensive_sim: 综合相似度 (几何50% + 位置50%)

		返回:
			(决策, 原因, 层级信息) 元组
		"""
		try:
			# 第二层：高相似度检查 (使用变量阈值)
			if comprehensive_sim >= self.__new_extreme_similarity_threshold:
				return 'skip', f'极高综合相似度 (综合={comprehensive_sim:.3f})', '第二层：高相似度检查'

			# 单维度极高相似度检查 (针对几何和位置)
			high_dimensions = [ ]
			if geometric_sim >= self.__new_single_dimension_high_threshold:
				high_dimensions.append( f'几何={geometric_sim:.3f}' )
			if position_sim >= self.__new_single_dimension_high_threshold:
				high_dimensions.append( f'位置={position_sim:.3f}' )

			if high_dimensions:
				# 检查另一个核心维度是否也达到基本要求
				other_dimension_ok = (
					(geometric_sim >= self.__new_core_dimension_threshold and
					 position_sim >= self.__new_core_dimension_threshold)
				)

				if other_dimension_ok:
					return 'skip', f'单维度极高相似度: {", ".join( high_dimensions )}', '第二层：高相似度检查'

			# 第四层：低相似度确认 (使用变量阈值)
			if comprehensive_sim <= self.__new_low_similarity_threshold:
				return 'add', f'低综合相似度，可以追加 (综合={comprehensive_sim:.3f})', '第四层：低相似度确认'

			# 关键维度否决检查 (针对几何和位置)
			if geometric_sim <= self.__new_geometric_veto_threshold:
				return 'add', f'几何维度否决，可以追加 (几何={geometric_sim:.3f})', '第四层：关键维度否决'

			if position_sim <= self.__new_position_veto_threshold:
				return 'add', f'位置维度否决，可以追加 (位置={position_sim:.3f})', '第四层：关键维度否决'

			# 第三层：中等相似度细化判断 (几何和位置维度一致性)
			if (geometric_sim >= self.__new_core_dimension_threshold and
				position_sim >= self.__new_core_dimension_threshold):
				return 'skip', f'核心维度一致性相似 (几何={geometric_sim:.3f}, 位置={position_sim:.3f})', '第三层：核心维度一致性验证'

			# 第五层：边界情况处理 - 模糊区间精细化判断
			if (self.__new_low_similarity_threshold < comprehensive_sim <
				self.__new_extreme_similarity_threshold):
				# 在模糊区间内，进行更细致的分析

				# 检查核心维度差异
				dimension_diff = abs( geometric_sim - position_sim )

				if dimension_diff > self.__new_dimension_diff_threshold:
					return 'add', f'核心维度差异过大，可以追加 (差异={dimension_diff:.3f})', '第五层：维度差异分析'

				# 检查综合相似度是否接近高阈值
				if comprehensive_sim >= self.__new_high_similarity_threshold:
					return 'skip', f'接近高相似度阈值 (综合={comprehensive_sim:.3f})', '第五层：边界情况处理'

				# 默认情况：中等相似度，倾向于追加
				return 'add', f'中等相似度，可以追加 (综合={comprehensive_sim:.3f})', '第五层：边界情况处理'

			# 兜底情况
			return 'add', f'未匹配任何决策规则，默认追加 (综合={comprehensive_sim:.3f})', '兜底决策'

		except Exception as e:
			self.__logger.warning( f"新架构智能决策失败: {str( e )}" )
			return 'add', f'决策失败，默认追加: {str( e )}', '异常处理'

	# ==================== 辅助计算方法 ====================

	def __calculate_polygon_area( self, points: np.ndarray ) -> float:
		"""计算多边形面积 (使用鞋带公式)"""
		try:
			if len( points ) < 3:
				return 0.0

			n = len( points )
			area = 0.0
			for i in range( n ):
				j = (i + 1) % n
				area += points[ i ][ 0 ] * points[ j ][ 1 ]
				area -= points[ j ][ 0 ] * points[ i ][ 1 ]

			return abs( area ) / 2.0
		except Exception:
			return 0.0

	def __calculate_polygon_perimeter( self, points: np.ndarray ) -> float:
		"""计算多边形周长"""
		try:
			if len( points ) < 2:
				return 0.0

			perimeter = 0.0
			n = len( points )
			for i in range( n ):
				j = (i + 1) % n
				distance = np.sqrt( (points[ j ][ 0 ] - points[ i ][ 0 ]) ** 2 + (points[ j ][ 1 ] - points[ i ][ 1 ]) ** 2 )
				perimeter += distance

			return perimeter
		except Exception:
			return 0.0

	def __calculate_polygon_centroid( self, points: np.ndarray ) -> Tuple[ float, float ]:
		"""计算多边形质心"""
		try:
			if len( points ) == 0:
				return (0.0, 0.0)

			# 简化计算：使用顶点平均值作为质心
			centroid_x = np.mean( points[ :, 0 ] )
			centroid_y = np.mean( points[ :, 1 ] )

			return (float( centroid_x ), float( centroid_y ))
		except Exception:
			return (0.0, 0.0)

	def __calculate_bounding_box( self, points: np.ndarray ) -> Dict[ str, float ]:
		"""计算边界框"""
		try:
			if len( points ) == 0:
				return { 'min_x': 0, 'min_y': 0, 'max_x': 0, 'max_y': 0 }

			min_x = float( np.min( points[ :, 0 ] ) )
			max_x = float( np.max( points[ :, 0 ] ) )
			min_y = float( np.min( points[ :, 1 ] ) )
			max_y = float( np.max( points[ :, 1 ] ) )

			return {
				'min_x': min_x,
				'min_y': min_y,
				'max_x': max_x,
				'max_y': max_y
			}
		except Exception:
			return { 'min_x': 0, 'min_y': 0, 'max_x': 0, 'max_y': 0 }

	def __calculate_bbox_overlap_similarity( self, bbox1: Dict[ str, float ], bbox2: Dict[ str, float ] ) -> float:
		"""计算边界框重叠相似度"""
		try:
			# 计算重叠区域
			overlap_left = max( bbox1[ 'min_x' ], bbox2[ 'min_x' ] )
			overlap_right = min( bbox1[ 'max_x' ], bbox2[ 'max_x' ] )
			overlap_top = max( bbox1[ 'min_y' ], bbox2[ 'min_y' ] )
			overlap_bottom = min( bbox1[ 'max_y' ], bbox2[ 'max_y' ] )

			if overlap_left >= overlap_right or overlap_top >= overlap_bottom:
				return 0.0  # 无重叠

			# 计算重叠面积
			overlap_area = (overlap_right - overlap_left) * (overlap_bottom - overlap_top)

			# 计算各自面积
			area1 = (bbox1[ 'max_x' ] - bbox1[ 'min_x' ]) * (bbox1[ 'max_y' ] - bbox1[ 'min_y' ])
			area2 = (bbox2[ 'max_x' ] - bbox2[ 'min_x' ]) * (bbox2[ 'max_y' ] - bbox2[ 'min_y' ])

			# 计算并集面积
			union_area = area1 + area2 - overlap_area

			if union_area <= 0:
				return 0.0

			return overlap_area / union_area
		except Exception:
			return 0.0

	def __calculate_convex_hull_area( self, points: np.ndarray ) -> float:
		"""计算凸包面积"""
		try:
			from scipy.spatial import ConvexHull

			if len( points ) < 3:
				return 0.0

			hull = ConvexHull( points )
			return hull.volume  # 在2D中，volume就是面积

		except ImportError:
			# scipy不可用时的降级方案：使用Graham扫描算法
			return self.__calculate_convex_hull_area_fallback( points )
		except Exception:
			return 0.0

	def __calculate_convex_hull_area_fallback( self, points: np.ndarray ) -> float:
		"""凸包面积计算的降级方案"""
		try:
			# 简化方案：使用边界框面积作为近似
			bbox = self.__calculate_bounding_box( points )
			return (bbox[ 'max_x' ] - bbox[ 'min_x' ]) * (bbox[ 'max_y' ] - bbox[ 'min_y' ])
		except Exception:
			return 0.0

	def __calculate_hausdorff_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""计算基于Hausdorff距离的形状相似度"""
		try:
			from scipy.spatial.distance import directed_hausdorff

			# 计算双向Hausdorff距离
			hausdorff_1_to_2 = directed_hausdorff( points1, points2 )[ 0 ]
			hausdorff_2_to_1 = directed_hausdorff( points2, points1 )[ 0 ]
			hausdorff_distance = max( hausdorff_1_to_2, hausdorff_2_to_1 )

			# 计算归一化因子
			bbox1 = self.__calculate_bounding_box( points1 )
			bbox2 = self.__calculate_bounding_box( points2 )
			diagonal1 = np.sqrt( (bbox1[ 'max_x' ] - bbox1[ 'min_x' ]) ** 2 + (bbox1[ 'max_y' ] - bbox1[ 'min_y' ]) ** 2 )
			diagonal2 = np.sqrt( (bbox2[ 'max_x' ] - bbox2[ 'min_x' ]) ** 2 + (bbox2[ 'max_y' ] - bbox2[ 'min_y' ]) ** 2 )
			max_diagonal = max( diagonal1, diagonal2 )

			if max_diagonal <= 0:
				return 0.0

			# 归一化Hausdorff距离
			normalized_hausdorff = hausdorff_distance / max_diagonal

			# 转换为相似度
			hausdorff_similarity = np.exp( -normalized_hausdorff * 5.0 )

			return float( max( 0.0, min( 1.0, hausdorff_similarity ) ) )

		except ImportError:
			# scipy不可用时的降级方案
			return self.__calculate_hausdorff_similarity_fallback( points1, points2 )
		except Exception:
			return 0.0

	def __calculate_hausdorff_similarity_fallback( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""Hausdorff距离相似度的降级方案"""
		try:
			# 简化方案：计算点集之间的平均最小距离
			def min_distance_to_set( point, point_set ):
				distances = [ np.sqrt( (point[ 0 ] - p[ 0 ]) ** 2 + (point[ 1 ] - p[ 1 ]) ** 2 ) for p in point_set ]
				return min( distances ) if distances else float( 'inf' )

			# 计算从points1到points2的最大最小距离
			max_min_dist_1_to_2 = max( [ min_distance_to_set( p, points2 ) for p in points1 ] )
			max_min_dist_2_to_1 = max( [ min_distance_to_set( p, points1 ) for p in points2 ] )
			hausdorff_distance = max( max_min_dist_1_to_2, max_min_dist_2_to_1 )

			# 归一化和转换为相似度
			bbox1 = self.__calculate_bounding_box( points1 )
			bbox2 = self.__calculate_bounding_box( points2 )
			diagonal1 = np.sqrt( (bbox1[ 'max_x' ] - bbox1[ 'min_x' ]) ** 2 + (bbox1[ 'max_y' ] - bbox1[ 'min_y' ]) ** 2 )
			diagonal2 = np.sqrt( (bbox2[ 'max_x' ] - bbox2[ 'min_x' ]) ** 2 + (bbox2[ 'max_y' ] - bbox2[ 'min_y' ]) ** 2 )
			max_diagonal = max( diagonal1, diagonal2 )

			if max_diagonal <= 0:
				return 0.0

			normalized_hausdorff = hausdorff_distance / max_diagonal
			hausdorff_similarity = np.exp( -normalized_hausdorff * 3.0 )  # 降级方案使用较宽松的参数

			return float( max( 0.0, min( 1.0, hausdorff_similarity ) ) )

		except Exception:
			return 0.0

	def __calculate_contour_matching_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""计算轮廓匹配相似度"""
		try:
			import cv2

			# 转换为OpenCV格式的轮廓
			contour1 = points1.astype( np.int32 ).reshape( -1, 1, 2 )
			contour2 = points2.astype( np.int32 ).reshape( -1, 1, 2 )

			# 使用OpenCV的matchShapes方法
			match_value = cv2.matchShapes( contour1, contour2, cv2.CONTOURS_MATCH_I1, 0.0 )

			# matchShapes返回值越小表示越相似，转换为相似度
			contour_similarity = np.exp( -match_value * 2.0 )

			return float( max( 0.0, min( 1.0, contour_similarity ) ) )

		except ImportError:
			# OpenCV不可用时的降级方案
			return self.__calculate_contour_matching_similarity_fallback( points1, points2 )
		except Exception:
			return 0.0

	def __calculate_contour_matching_similarity_fallback( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""轮廓匹配相似度的降级方案"""
		try:
			# 简化方案：比较多边形的角度分布
			def calculate_angles( points ):
				angles = [ ]
				n = len( points )
				for i in range( n ):
					p1 = points[ (i - 1) % n ]
					p2 = points[ i ]
					p3 = points[ (i + 1) % n ]

					v1 = p1 - p2
					v2 = p3 - p2

					# 计算角度
					cos_angle = np.dot( v1, v2 ) / (np.linalg.norm( v1 ) * np.linalg.norm( v2 ) + 1e-10)
					angle = np.arccos( np.clip( cos_angle, -1.0, 1.0 ) )
					angles.append( angle )

				return np.array( angles )

			angles1 = calculate_angles( points1 )
			angles2 = calculate_angles( points2 )

			# 如果点数不同，使用插值或截断
			if len( angles1 ) != len( angles2 ):
				min_len = min( len( angles1 ), len( angles2 ) )
				angles1 = angles1[ :min_len ]
				angles2 = angles2[ :min_len ]

			if len( angles1 ) == 0:
				return 0.0

			# 计算角度差异
			angle_diff = np.mean( np.abs( angles1 - angles2 ) )
			contour_similarity = np.exp( -angle_diff )

			return float( max( 0.0, min( 1.0, contour_similarity ) ) )

		except Exception:
			return 0.0

	def __calculate_convex_hull_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""计算凸包相似度"""
		try:
			# 计算凸包面积
			convex_area1 = self.__calculate_convex_hull_area( points1 )
			convex_area2 = self.__calculate_convex_hull_area( points2 )

			if convex_area1 <= 0 or convex_area2 <= 0:
				return 0.0

			# 计算面积比例相似度
			area_ratio = min( convex_area1, convex_area2 ) / max( convex_area1, convex_area2 )

			return float( max( 0.0, min( 1.0, area_ratio ) ) )

		except Exception:
			return 0.0

	def __calculate_shape_descriptor_similarity( self, points1: np.ndarray, points2: np.ndarray ) -> float:
		"""计算形状描述符相似度"""
		try:
			# 简化的形状描述符：使用边长比例作为特征
			def calculate_edge_ratios( points ):
				if len( points ) < 3:
					return np.array( [ ] )

				edges = [ ]
				n = len( points )
				for i in range( n ):
					j = (i + 1) % n
					edge_length = np.sqrt( (points[ j ][ 0 ] - points[ i ][ 0 ]) ** 2 + (points[ j ][ 1 ] - points[ i ][ 1 ]) ** 2 )
					edges.append( edge_length )

				edges = np.array( edges )
				if np.sum( edges ) <= 0:
					return np.array( [ ] )

				# 归一化边长
				normalized_edges = edges / np.sum( edges )
				return normalized_edges

			ratios1 = calculate_edge_ratios( points1 )
			ratios2 = calculate_edge_ratios( points2 )

			if len( ratios1 ) == 0 or len( ratios2 ) == 0:
				return 0.0

			# 如果边数不同，使用较短的长度
			min_len = min( len( ratios1 ), len( ratios2 ) )
			ratios1 = ratios1[ :min_len ]
			ratios2 = ratios2[ :min_len ]

			# 计算欧几里得距离
			descriptor_distance = np.linalg.norm( ratios1 - ratios2 )
			descriptor_similarity = np.exp( -descriptor_distance * 5.0 )

			return float( max( 0.0, min( 1.0, descriptor_similarity ) ) )

		except Exception:
			return 0.0

	# ==================== 配置和管理方法 ====================

	def configure_weights(
			self,
			geometric_weight: float = 0.50,
			position_weight: float = 0.50,
			scale_weight: float = 0.00,
			shape_weight: float = 0.00
	):
		"""
		配置相似度权重 (新架构)

		注意：在新架构中，只有几何和位置相似度参与综合相似度计算
		尺度和形状相似度仅作为前提条件使用，不参与权重计算

		参数:
			geometric_weight: 几何相似度权重，默认50% (实际使用)
			position_weight: 位置相似度权重，默认50% (实际使用)
			scale_weight: 尺度相似度权重，默认0% (仅作前提条件，不参与计算)
			shape_weight: 形状相似度权重，默认0% (仅作前提条件，不参与计算)

		使用示例:
			# 调整几何和位置的权重比例
			processor.configure_weights(
				geometric_weight=0.60,
				position_weight=0.40,
				scale_weight=0.00,  # 必须为0
				shape_weight=0.00   # 必须为0
			)
		"""
		# 验证核心权重总和 (只验证几何和位置)
		core_weight_total = geometric_weight + position_weight
		if abs( core_weight_total - 1.0 ) > 0.01:
			self.__logger.warning( f"几何和位置权重总和为{core_weight_total:.3f}，建议调整为1.0" )

		# 验证尺度和形状权重为0
		if scale_weight != 0.0 or shape_weight != 0.0:
			self.__logger.warning( f"新架构中尺度和形状权重应为0，当前值: 尺度={scale_weight}, 形状={shape_weight}" )

		# 更新权重
		self.__geometric_weight = geometric_weight
		self.__position_weight = position_weight
		self.__scale_weight = scale_weight
		self.__shape_weight = shape_weight

		self.__logger.info(
			f"相似度权重已更新: 几何={geometric_weight:.2f}, 位置={position_weight:.2f}, "
			f"尺度={scale_weight:.2f}, 形状={shape_weight:.2f}"
		)

		if self.__log_output:
			self.__log_output.append(
				f"坐标相似度权重已更新: 几何={geometric_weight:.0%}, 位置={position_weight:.0%}, "
				f"尺度={scale_weight:.0%}, 形状={shape_weight:.0%}",
				Colors.GREEN
			)

	def configure_thresholds(
			self,
			extreme_threshold: float = None,
			high_threshold: float = None,
			medium_threshold: float = None,
			low_threshold: float = None
	):
		"""
		配置决策阈值

		参数:
			extreme_threshold: 极高相似度阈值
			high_threshold: 高相似度阈值
			medium_threshold: 中等相似度阈值
			low_threshold: 低相似度阈值

		使用示例:
			# 设置更严格的阈值
			processor.configure_thresholds(
				extreme_threshold=0.95,
				high_threshold=0.80,
				low_threshold=0.35
			)
		"""
		if extreme_threshold is not None:
			self.__extreme_similarity_threshold = extreme_threshold
		if high_threshold is not None:
			self.__high_similarity_threshold = high_threshold
		if medium_threshold is not None:
			self.__medium_similarity_threshold = medium_threshold
		if low_threshold is not None:
			self.__low_similarity_threshold = low_threshold

		self.__logger.info(
			f"决策阈值已更新: 极高≥{self.__extreme_similarity_threshold:.2f}, "
			f"高≥{self.__high_similarity_threshold:.2f}, "
			f"中等≥{self.__medium_similarity_threshold:.2f}, "
			f"低<{self.__low_similarity_threshold:.2f}"
		)

		if self.__log_output:
			self.__log_output.append(
				f"坐标相似度阈值已更新: 极高≥{self.__extreme_similarity_threshold:.0%}, "
				f"高≥{self.__high_similarity_threshold:.0%}, 低<{self.__low_similarity_threshold:.0%}",
				Colors.GREEN
			)

	def clear_cache( self ):
		"""清空计算缓存"""
		with self.__cache_lock:
			self.__calculation_cache.clear()
		self.__logger.info( "坐标相似度计算缓存已清空" )

	def get_cache_info( self ) -> Dict[ str, Any ]:
		"""获取缓存信息"""
		with self.__cache_lock:
			return {
				'cache_size': len( self.__calculation_cache ),
				'max_cache_size': self.__max_cache_size,
				'cache_keys': list( self.__calculation_cache.keys() )[ :10 ]  # 只显示前10个键
			}

	def get_performance_stats( self ) -> Dict[ str, Any ]:
		"""获取性能统计信息"""
		with self.__performance_lock:
			avg_time = (self.__total_calculation_time / self.__calculation_count
			            if self.__calculation_count > 0 else 0.0)
			return {
				'calculation_count': self.__calculation_count,
				'total_calculation_time': self.__total_calculation_time,
				'average_calculation_time': avg_time,
				'calculations_per_second': (self.__calculation_count / self.__total_calculation_time
				                            if self.__total_calculation_time > 0 else 0.0)
			}

	def get_configuration( self ) -> Dict[ str, Any ]:
		"""获取当前配置信息"""
		return {
			'mode': self.__mode,
			'weights': {
				'geometric': self.__geometric_weight,
				'position': self.__position_weight,
				'scale': self.__scale_weight,
				'shape': self.__shape_weight
			},
			'thresholds': {
				'extreme_similarity': self.__extreme_similarity_threshold,
				'high_similarity': self.__high_similarity_threshold,
				'medium_similarity': self.__medium_similarity_threshold,
				'low_similarity': self.__low_similarity_threshold
			},
			'fine_tuning_thresholds': {
				'single_dimension_high': self.__single_dimension_high_threshold,
				'multi_dimension': self.__multi_dimension_threshold,
				'key_dimension_geometric': self.__key_dimension_geometric_threshold,
				'key_dimension_position': self.__key_dimension_position_threshold,
				'geometric_veto': self.__geometric_veto_threshold,
				'position_veto': self.__position_veto_threshold
			},
			'geometric_parameters': {
				'min_area_threshold': self.__min_area_threshold,
				'max_size_ratio': self.__max_size_ratio,
				'distance_threshold_factor': self.__distance_threshold_factor,
				'log_threshold': self.__log_threshold
			}
		}
